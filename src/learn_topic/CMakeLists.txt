cmake_minimum_required(VERSION 3.8)
project(learn_topic)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)

# 创建发布者可执行文件
add_executable(pub_hello_world src/pub_hello_world.cpp)
ament_target_dependencies(pub_hello_world rclcpp std_msgs)

# 创建订阅者可执行文件
add_executable(sub_hello_world src/sub_hello_world.cpp)
ament_target_dependencies(sub_hello_world rclcpp std_msgs)

install(TARGETS
  pub_hello_world
  sub_hello_world
  DESTINATION lib/${PROJECT_NAME})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
