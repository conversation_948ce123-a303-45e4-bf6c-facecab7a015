#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "cv_bridge/cv_bridge.h"
#include <opencv2/opencv.hpp>

class SubImg : public rclcpp::Node
{
public:
    SubImg();
    ~SubImg();

private:
    void image_callback(const sensor_msgs::msg::Image::SharedPtr msg);
    
    rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr image_subscription_;
};

// 构造函数实现
SubImg::SubImg() : Node("sub_img")
{
    // 创建图像订阅者，订阅 "camera/raw" 话题
    image_subscription_ = this->create_subscription<sensor_msgs::msg::Image>(
        "camera/raw", 
        10, 
        std::bind(&SubImg::image_callback, this, std::placeholders::_1));
    
    RCLCPP_INFO(this->get_logger(), "图像订阅者启动！订阅话题: camera/raw");
}

// 析构函数实现
SubImg::~SubImg()
{
    RCLCPP_INFO(this->get_logger(), "图像订阅者停止！");
}

// 图像回调函数
void SubImg::image_callback(const sensor_msgs::msg::Image::SharedPtr msg)
{
    RCLCPP_INFO(this->get_logger(), "收到图像回调！");

    try {
        // 显示图像信息
        RCLCPP_INFO(this->get_logger(),
            "接收到图像: %dx%d, 编码: %s",
            msg->width, msg->height, msg->encoding.c_str());

        // 将ROS2图像消息转换为OpenCV格式
        cv_bridge::CvImagePtr cv_ptr = cv_bridge::toCvCopy(msg, "bgr8");

        // 显示图像（如果有显示器）
        cv::imshow("Camera Feed", cv_ptr->image);
        cv::waitKey(1);

        RCLCPP_INFO(this->get_logger(), "图像处理完成");

    } catch (cv_bridge::Exception& e) {
        RCLCPP_ERROR(this->get_logger(), "cv_bridge异常: %s", e.what());
    } catch (std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "其他异常: %s", e.what());
    }
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    
    // 创建图像订阅者对象
    auto node = std::make_shared<SubImg>();
    
    // 保持节点运行
    rclcpp::spin(node);
    
    // 清理OpenCV窗口
    cv::destroyAllWindows();
    
    rclcpp::shutdown();
    return 0;
}
