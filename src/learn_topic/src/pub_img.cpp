#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "cv_bridge/cv_bridge.h"
#include <opencv2/opencv.hpp>
#include <chrono>

using namespace std::chrono_literals;

class PubImg : public rclcpp::Node
{
public:
    PubImg();
    ~PubImg();

private:
    void capture_and_publish();

    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr image_publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
    cv::VideoCapture cap_;
};

// 构造函数实现
PubImg::PubImg() : Node("pub_img")
{
    // 创建图像发布者，发布到 "camera/raw" 话题
    image_publisher_ = this->create_publisher<sensor_msgs::msg::Image>("camera/raw", 10);

    // 初始化摄像头（0表示默认摄像头）
    cap_.open(0);
    if (!cap_.isOpened()) {
        RCLCPP_ERROR(this->get_logger(), "无法打开摄像头！");
        return;
    }

    // 设置摄像头参数
    cap_.set(cv::CAP_PROP_FRAME_WIDTH, 640);
    cap_.set(cv::CAP_PROP_FRAME_HEIGHT, 480);
    cap_.set(cv::CAP_PROP_FPS, 30);

    // 创建定时器，每33毫秒发布一次（约30fps）
    timer_ = this->create_wall_timer(
        33ms, std::bind(&PubImg::capture_and_publish, this));

    RCLCPP_INFO(this->get_logger(), "摄像头发布者启动！发布到话题: camera/raw");
}

// 析构函数实现
PubImg::~PubImg()
{
    if (cap_.isOpened()) {
        cap_.release();
    }
    RCLCPP_INFO(this->get_logger(), "摄像头发布者停止！");
}

// 捕获并发布图像的函数
void PubImg::capture_and_publish()
{
    cv::Mat frame;

    // 从摄像头捕获一帧
    if (!cap_.read(frame)) {
        RCLCPP_WARN(this->get_logger(), "无法从摄像头读取图像");
        return;
    }

    // 将OpenCV图像转换为ROS2消息
    std_msgs::msg::Header header;
    header.stamp = this->get_clock()->now();
    header.frame_id = "camera_frame";

    auto cv_bridge_ptr = cv_bridge::CvImage(header, "bgr8", frame).toImageMsg();

    // 发布图像
    image_publisher_->publish(*cv_bridge_ptr);

    RCLCPP_DEBUG(this->get_logger(), "发布了一帧图像，尺寸: %dx%d", frame.cols, frame.rows);
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    // 创建摄像头发布者对象
    auto node = std::make_shared<PubImg>();

    // 保持节点运行
    rclcpp::spin(node);

    rclcpp::shutdown();
    return 0;
}