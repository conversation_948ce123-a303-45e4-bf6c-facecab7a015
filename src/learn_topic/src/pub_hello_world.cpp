#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"


using namespace std::chrono_literals;

class PubHelloWorld : public rclcpp::Node
{
public:
    PubHelloWorld();
    ~PubHelloWorld();

private:
    void publish_message();

    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publisher_;
    rclcpp::TimerBase::SharedPtr timer_;
    size_t count_;
};

// 构造函数实现
PubHelloWorld::PubHelloWorld() : Node("pub_hello_world"), count_(0)   // 初始化列表
{
    // 创建发布者，发布到 "hello_world" 话题
    publisher_ = this->create_publisher<std_msgs::msg::String>("hello_world",10);

    // 创建定时器，每0.5秒调用一次 publish_message 函数
    timer_ = this->create_wall_timer(500ms,std::bind(&PubHelloWorld::publish_message, this));

    RCLCPP_INFO(this->get_logger(), "Hello World Publisher started!");
}

// 析构函数实现
PubHelloWorld::~PubHelloWorld()
{
    RCLCPP_INFO(this->get_logger(), "Hello World Publisher stopped!");
}

// 发布消息的回调函数
void PubHelloWorld::publish_message()
{
    auto message = std_msgs::msg::String();
    message.data = "Hello World! x Count: " + std::to_string(count_++);

    // RCLCPP_INFO(this->get_logger(), "Publishing: '%s'", message.data.c_str());
    publisher_->publish(message);
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    // 创建发布者对象
    auto node = std::make_shared<PubHelloWorld>();

    // 保持节点运行
    rclcpp::spin(node);

    rclcpp::shutdown();
    return 0;
}