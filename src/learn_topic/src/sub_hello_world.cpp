#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"

class SubHelloWorld : public rclcpp::Node
{
public:
    SubHelloWorld();
    ~SubHelloWorld();

private:
    void subscribe_message_callback(const std_msgs::msg::String::SharedPtr msg);

    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subscription_;
};

// 构造函数实现
SubHelloWorld::SubHelloWorld() : Node("sub_hello_world")
{
    // 创建订阅者，订阅 "hello_world" 话题
    subscription_ = this->create_subscription<std_msgs::msg::String>(
        "hello_world",
        10,
        std::bind(&SubHelloWorld::subscribe_message_callback, this, std::placeholders::_1));

    RCLCPP_INFO(this->get_logger(), "Hello World Subscriber started!");
}

// 析构函数实现
SubHelloWorld::~SubHelloWorld()
{
    RCLCPP_INFO(this->get_logger(), "Hello World Subscriber stopped!");
}

// 回调函数实现
void SubHelloWorld::subscribe_message_callback(const std_msgs::msg::String::SharedPtr msg)
{
    RCLCPP_INFO(this->get_logger(), "Received message: '%s'", msg->data.c_str());
}

int main(int argc, char * argv[])
{
  rclcpp::init(argc, argv);

  auto node = std::make_shared<SubHelloWorld>();

  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}
