# - Config file for the learn_service package
# It defines the following variables
#  learn_service_INCLUDE_DIRS - include directories for FooBar
#  learn_service_LIBRARIES    - libraries to link against
#  learn_service_EXECUTABLE   - the bar executable

set(learn_service_INCLUDE_DIRS "@CONF_INCLUDE_DIRS@")

# Our library dependencies (contains definitions for IMPORTED targets)
include("${learn_service_DIR}/export_learn_service.cmake")

# These are IMPORTED targets created by learn_serviceTargets.cmake

set(learn_service_EXECUTABLE service_client)

