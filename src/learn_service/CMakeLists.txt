cmake_minimum_required(VERSION 3.14)
project(learn_service)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(example_interfaces REQUIRED)
# find_package(chrono REQUIRED)
# 创建发布者可执行文件
add_executable(service_client src/service_client.cpp)
ament_target_dependencies(service_client rclcpp example_interfaces )

# 创建订阅者可执行文件
add_executable(service_server src/service_server.cpp)
ament_target_dependencies(service_server rclcpp example_interfaces)



install(TARGETS
  service_server
  service_client

  DESTINATION lib/${PROJECT_NAME})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
