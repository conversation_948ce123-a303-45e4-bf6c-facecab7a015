cmake_minimum_required(VERSION 3.14)
project(learn_service)

set(learn_service_MAJOR_VERSION 0)
set(learn_service_MINOR_VERSION 0)
set(learn_service_PATCH_VERSION 0)
set(learn_service_VERSION
  ${learn_service_MAJOR_VERSION}.${learn_service_MINOR_VERSION}.${learn_service_PATCH_VERSION})

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)

add_executable(service_client src/service_client.cpp)
target_compile_features(service_client PUBLIC c_std_99 cxx_std_17)  # Require C99 and C++17

install(
  TARGETS service_client
  EXPORT export_learn_service
  DESTINATION lib/${PROJECT_NAME})

# export targets
set(export_targets ${export_targets};service_client)
export(EXPORT export_learn_service
  FILE "${PROJECT_BINARY_DIR}/export_learn_service.cmake")

# Create the learn_serviceConfig.cmake
set(CONF_INCLUDE_DIRS "${CMAKE_INSTALL_PREFIX}/include")
configure_file(learn_serviceConfig.cmake.in
  "${PROJECT_BINARY_DIR}/${CMAKE_FILES_DIRECTORY}/learn_serviceConfig.cmake" @ONLY)

# Create the learn_serviceConfigVersion.cmake
configure_file(learn_serviceConfigVersion.cmake.in
  "${PROJECT_BINARY_DIR}/learn_serviceConfigVersion.cmake" @ONLY)

install(FILES
  "${PROJECT_BINARY_DIR}/${CMAKE_FILES_DIRECTORY}/learn_serviceConfig.cmake"
  "${PROJECT_BINARY_DIR}/learn_serviceConfigVersion.cmake"
  DESTINATION "share/${PROJECT_NAME}/cmake" COMPONENT dev)
install(EXPORT export_learn_service
  DESTINATION "share/${PROJECT_NAME}/cmake"
  FILE export_learn_service.cmake
  COMPONENT dev)
