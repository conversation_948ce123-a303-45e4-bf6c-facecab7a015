#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "cv_bridge/cv_bridge.h"
#include <opencv2/opencv.hpp>
#include <chrono>

using namespace std::chrono_literals;

class Service : public rclcpp::Node
{
public:
    Service();
    ~Service();

private:
    void service_callback();

    rclcpp::create_service ;
    rclcpp::TimerBase::SharedPtr timer_;
    cv::VideoCapture cap_;
};

// 构造函数实现
Service::Service():Node("service_server")
{
    service_ = this->create_service<example_interfaces::srv::AddTwoInts>(
            "add_two_ints",
            std::bind(&MyServiceServer::add_two_ints_callback, this,
                      std::placeholders::_1, std::placeholders::_2));
}