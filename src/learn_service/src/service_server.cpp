#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>


class Service : public rclcpp::Node
{
public:
    Service();
    ~Service();

private:
    void add_two_ints_callback(
    const std::shared_ptr<example_interfaces::srv::AddTwoInts::Request> request,
    std::shared_ptr<example_interfaces::srv::AddTwoInts::Response> response);

    rclcpp::Service<example_interfaces::srv::AddTwoInts>::SharedPtr service_;
    rclcpp::TimerBase::SharedPtr timer_;

};

// 构造函数实现
Service::Service():Node("service_server")
{
    // 创建服务
    RCLCPP_INFO(this->get_logger(), "Service Server started!");
    service_ = this->create_service<example_interfaces::srv::AddTwoInts>(
            "add_two_ints",
            std::bind(&Service::add_two_ints_callback, this,
                      std::placeholders::_1, std::placeholders::_2));
}
Service::~Service()
{
    RCLCPP_INFO(this->get_logger(), "Service Server stopped!");
}

void Service::add_two_ints_callback(
    const std::shared_ptr<example_interfaces::srv::AddTwoInts::Request> request,
    std::shared_ptr<example_interfaces::srv::AddTwoInts::Response> response)
{
    response->sum = request->a + request->b;
    RCLCPP_INFO(this->get_logger(), "Incoming request a: %ld, b: %ld", request->a, request->b);
    RCLCPP_INFO(this->get_logger(), "Sending back response: [%ld]", (long int)response->sum);
}


int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    auto node = std::make_shared<Service>();

    rclcpp::spin(node);

    rclcpp::shutdown();
    return 0;
}