#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>

using namespace std::chrono_literals;

class Service : public rclcpp::Node
{
public:
    Service();
    ~Service();

private:
    void add_two_ints_callback();

    rclcpp::Service<example_interfaces::srv::AddTwoInts>::SharedPtr service_;
    rclcpp::TimerBase::SharedPtr timer_;

};

// 构造函数实现
Service::Service():Node("service_server")
{
    service_ = this->create_service<example_interfaces::srv::AddTwoInts>(
            "add_two_ints",
            std::bind(&Service::add_two_ints_callback, this,
                      std::placeholders::_1, std::placeholders::_2));
}