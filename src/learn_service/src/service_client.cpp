#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>


class ServiceClinet : public rclcpp::Node
{
public:
    ServiceClinet();
    ~ServiceClinet();

private:
    void send_request();

    rclcpp::Client<example_interfaces::srv::AddTwoInts>::SharedPtr clinet_;
    rclcpp::TimerBase::SharedPtr timer_;

};

ServiceClinet::ServiceClinet():Node("service_client")
{
    // 创建客户端
    RCLCPP_INFO(this->get_logger(), "Service Client started!");
    clinet_ = this->create_client<example_interfaces::srv::AddTwoInts>("add_two_ints");
}

void ServiceClinet::send_request(int64_t a, int64_t b)
{
  while(!clinet_->wait_for_service(std::chrono::seconds(1)))
  {
    if(!rclcpp::ok())
    {
      RCLCPP_ERROR(this->get_logger(), "Interrupted while waiting for service");
      return;
    }
    RCLCPP_INFO(this->get_logger(), "Service not available, waiting again...");
  }
    // 创建请求
  auto request = std::make_shared<example_interfaces::srv::AddTwoInts::Request>();
  request->a = a;
  request->b = b;

  RCLCPP_INFO(this->get_logger(), "Sending request: %ld + %ld", a, b);

  auto result_future = clinet_->async_send_request(request);

}


int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    auto node = std::make_shared<Service>();

    rclcpp::spin(node);

    rclcpp::shutdown();
    return 0;
}