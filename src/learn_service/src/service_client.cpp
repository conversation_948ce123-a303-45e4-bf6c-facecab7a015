#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>


class Clinet : public rclcpp::Node
{
public:
    Clinet();
    ~Clinet();

private:
    void add_two_ints_callback();

    rclcpp::Client<example_interfaces::srv::AddTwoInts>::SharedPtr clinet_;
    rclcpp::TimerBase::SharedPtr timer_;

};

Clinet::Clinet():Node("service_client")
{
    // 创建客户端
    RCLCPP_INFO(this->get_logger(), "Service Client started!");
    clinet_ = this->create_client<example_interfaces::srv::AddTwoInts>("add_two_ints");
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    auto node = std::make_shared<Service>();

    rclcpp::spin(node);

    rclcpp::shutdown();
    return 0;
}