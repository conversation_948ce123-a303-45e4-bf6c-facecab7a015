#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>


class ServiceClinet : public rclcpp::Node
{
public:
    ServiceClinet();
    ~ServiceClinet();

private:
    void send_request();

    rclcpp::Client<example_interfaces::srv::AddTwoInts>::SharedPtr clinet_;
    rclcpp::TimerBase::SharedPtr timer_;

};

ServiceClinet::ServiceClinet():Node("service_client")
{
    // 创建客户端
    RCLCPP_INFO(this->get_logger(), "Service Client started!");
    clinet_ = this->create_client<example_interfaces::srv::AddTwoInts>("add_two_ints");
}

void ServiceClinet::send_request(int64_t a, int64_t b)
{
  while(!clinet_->wait_for_service(1s))
  {
    if(!rclcpp::ok())
    {
      RCLCPP_ERROR(this->get_logger(), "Interrupted while waiting for service");
      return;
    }
    RCLCPP_INFO(this->get_logger(), "Service not available, waiting again...");
  }
    // 创建请求
  auto request = std::make_shared<example_interfaces::srv::AddTwoInts::Request>();
  request->a = a;
  request->b = b;

  RCLCPP_INFO(this->get_logger(), "Sending request: %ld + %ld", a, b);

  auto result_future = clinet_->async_send_request(request);
  // 等待响应
  if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), result_future) ==
      rclcpp::FutureReturnCode::SUCCESS)
    {
      respond = result_future.get();
      RCLCPP_INFO(this->get_logger(),"收到响应: %ld + %ld = %ld", a, b, respond->sum);
    }
  else
  {
    RCLCPP_ERROR(this->get_logger(), "调用服务失败");
  }
}


int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    if(argc!=3)
    {
      RCLCPP_ERROR(rclcpp::get_logger("main"), "Usage: ros2 run learn_service service_client <num1> <num2>");
      return 1;
    }
    int a,b;
    try
    {
      a = std::stoll(argv[1]);
      b = std::stoll(argv[2]);
    }
    catch(const std::exception& e)
    {
      RCLCPP_INFO(rclcpp::get_logger("exception:%s"),e.what);
      return 1;
    }
    

    auto client_node = std::make_shared<ServiceClinet>();
    client_node->send_request(a, b);

    rclcpp::shutdown();
    return 0;
}