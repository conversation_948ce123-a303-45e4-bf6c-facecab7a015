#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>


class Clinet : public rclcpp::Node
{
public:
    Clinet();
    ~Clinet();

private:
    void add_two_ints_callback();

    rclcpp::Service<example_interfaces::srv::AddTwoInts>::SharedPtr service_;
    rclcpp::TimerBase::SharedPtr timer_;

};



int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    auto node = std::make_shared<Service>();

    rclcpp::spin(node);

    rclcpp::shutdown();
    return 0;
}