#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>
#include <memory>

using namespace std::chrono_literals;

class SimpleServiceClient : public rclcpp::Node
{
public:
    SimpleServiceClient();
    void send_request(int64_t a, int64_t b);

private:
    rclcpp::Client<example_interfaces::srv::AddTwoInts>::SharedPtr client_;
};

// 构造函数实现
SimpleServiceClient::SimpleServiceClient() : Node("simple_service_client")
{
    // 创建客户端，连接到 "add_two_ints" 服务
    client_ = this->create_client<example_interfaces::srv::AddTwoInts>("add_two_ints");
    
    RCLCPP_INFO(this->get_logger(), "服务客户端已启动");
}

// 发送请求的函数
void SimpleServiceClient::send_request(int64_t a, int64_t b)
{
    // 等待服务可用
    while (!client_->wait_for_service(1s)) {
        if (!rclcpp::ok()) {
            RCLCPP_ERROR(this->get_logger(), "等待服务时被中断");
            return;
        }
        RCLCPP_INFO(this->get_logger(), "等待服务 'add_two_ints' 可用...");
    }

    // 创建请求
    auto request = std::make_shared<example_interfaces::srv::AddTwoInts::Request>();
    request->a = a;
    request->b = b;

    RCLCPP_INFO(this->get_logger(), "发送请求: %ld + %ld", a, b);

    // 发送异步请求
    auto result_future = client_->async_send_request(request);

    // 等待响应
    if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), result_future) ==
        rclcpp::FutureReturnCode::SUCCESS)
    {
        auto response = result_future.get();
        RCLCPP_INFO(this->get_logger(), "收到响应: %ld + %ld = %ld", 
                    a, b, response->sum);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "调用服务失败");
    }
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    auto client_node = std::make_shared<SimpleServiceClient>();

    // 发送几个测试请求
    client_node->send_request(5, 3);
    client_node->send_request(10, 20);
    client_node->send_request(-5, 15);

    rclcpp::shutdown();
    return 0;
}
