#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"
#include <chrono>
#include <memory>

using namespace std::chrono_literals;

class SimpleServiceClient : public rclcpp::Node
{
public:
    SimpleServiceClient();
    void send_request(int64_t a, int64_t b);

private:
    rclcpp::Client<example_interfaces::srv::AddTwoInts>::SharedPtr client_;
};

// 构造函数实现
SimpleServiceClient::SimpleServiceClient() : Node("simple_service_client")
{
    // 创建客户端，连接到 "add_two_ints" 服务
    client_ = this->create_client<example_interfaces::srv::AddTwoInts>("add_two_ints");
    
    RCLCPP_INFO(this->get_logger(), "服务客户端已启动");
}

// 发送请求的函数
void SimpleServiceClient::send_request(int64_t a, int64_t b)
{
    // 等待服务可用
    while (!client_->wait_for_service(1s)) {
        if (!rclcpp::ok()) {
            RCLCPP_ERROR(this->get_logger(), "等待服务时被中断");
            return;
        }
        RCLCPP_INFO(this->get_logger(), "等待服务 'add_two_ints' 可用...");
    }

    // 创建请求
    auto request = std::make_shared<example_interfaces::srv::AddTwoInts::Request>();
    request->a = a;
    request->b = b;

    RCLCPP_INFO(this->get_logger(), "发送请求: %ld + %ld", a, b);

    // 发送异步请求
    auto result_future = client_->async_send_request(request);

    // 等待响应
    if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), result_future) ==
        rclcpp::FutureReturnCode::SUCCESS)
    {
        auto response = result_future.get();
        RCLCPP_INFO(this->get_logger(), "收到响应: %ld + %ld = %ld", 
                    a, b, response->sum);
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(), "调用服务失败");
    }
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    // 检查命令行参数
    if (argc != 3) {
        RCLCPP_ERROR(rclcpp::get_logger("main"),
                     "用法: ros2 run learn_service simple_service_client <数字1> <数字2>");
        RCLCPP_ERROR(rclcpp::get_logger("main"),
                     "例如: ros2 run learn_service simple_service_client 5 3");
        return 1;
    }

    // 解析命令行参数
    int64_t a, b;
    try {
        a = std::stoll(argv[1]);
        b = std::stoll(argv[2]);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("main"),
                     "参数必须是有效的整数");
        return 1;
    }

    auto client_node = std::make_shared<SimpleServiceClient>();

    // 发送用户指定的请求
    client_node->send_request(a, b);

    rclcpp::shutdown();
    return 0;
}
