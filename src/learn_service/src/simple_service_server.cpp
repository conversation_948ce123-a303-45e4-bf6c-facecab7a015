#include "rclcpp/rclcpp.hpp"
#include "example_interfaces/srv/add_two_ints.hpp"

class SimpleServiceServer : public rclcpp::Node
{
public:
    SimpleServiceServer();

private:
    void add_two_ints_callback(
        const std::shared_ptr<example_interfaces::srv::AddTwoInts::Request> request,
        std::shared_ptr<example_interfaces::srv::AddTwoInts::Response> response);

    rclcpp::Service<example_interfaces::srv::AddTwoInts>::SharedPtr service_;
};

// 构造函数实现
SimpleServiceServer::SimpleServiceServer() : Node("simple_service_server")
{
    // 创建服务
    service_ = this->create_service<example_interfaces::srv::AddTwoInts>(
        "add_two_ints",
        std::bind(&SimpleServiceServer::add_two_ints_callback, this,
                  std::placeholders::_1, std::placeholders::_2));

    RCLCPP_INFO(this->get_logger(), "加法服务已启动，等待请求...");
}

// 服务回调函数
void SimpleServiceServer::add_two_ints_callback(
    const std::shared_ptr<example_interfaces::srv::AddTwoInts::Request> request,
    std::shared_ptr<example_interfaces::srv::AddTwoInts::Response> response)
{
    // 执行加法计算
    response->sum = request->a + request->b;
    
    // 打印日志
    RCLCPP_INFO(this->get_logger(), 
                "收到请求: %ld + %ld，返回结果: %ld", 
                request->a, request->b, response->sum);
}

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    auto server_node = std::make_shared<SimpleServiceServer>();

    // 保持服务运行
    rclcpp::spin(server_node);

    rclcpp::shutdown();
    return 0;
}
