[0.009s] Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[0.048s] [35m[1mConsolidate compiler generated dependencies of target service_server[0m
[0.048s] [35m[1mConsolidate compiler generated dependencies of target service_client[0m
[0.066s] [ 50%] [32mBuilding CXX object CMakeFiles/service_client.dir/src/service_client.cpp.o[0m
[0.066s] [ 75%] Built target service_server
[2.813s] [100%] [32m[1mLinking CXX executable service_client[0m
[2.910s] /usr/bin/ld: CMakeFiles/service_client.dir/src/service_client.cpp.o: warning: relocation against `_ZTV13ServiceClient' in read-only section `.text'
[2.916s] /usr/bin/ld: CMakeFiles/service_client.dir/src/service_client.cpp.o: in function `ServiceClient::ServiceClient()':
[2.916s] service_client.cpp:(.text+0xf7): undefined reference to `vtable for ServiceClient'
[2.926s] /usr/bin/ld: warning: creating DT_TEXTREL in a PIE
[2.929s] collect2: error: ld returned 1 exit status
[2.931s] gmake[2]: *** [CMakeFiles/service_client.dir/build.make:175：service_client] 错误 1
[2.931s] gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/service_client.dir/all] 错误 2
[2.932s] gmake: *** [Makefile:146：all] 错误 2
[2.934s] Invoked command in '/home/<USER>/code/test/build/learn_service' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
