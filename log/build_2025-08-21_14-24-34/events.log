[0.000000] (-) TimerEvent: {}
[0.000391] (-) JobUnselected: {'identifier': 'learn_topic'}
[0.000467] (-) JobUnselected: {'identifier': 'pkg1'}
[0.000510] (learn_service) JobQueued: {'identifier': 'learn_service', 'dependencies': OrderedDict()}
[0.000541] (learn_service) JobStarted: {'identifier': 'learn_service'}
[0.006812] (learn_service) JobProgress: {'identifier': 'learn_service', 'progress': 'cmake'}
[0.007268] (learn_service) JobProgress: {'identifier': 'learn_service', 'progress': 'build'}
[0.007855] (learn_service) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/test/build/learn_service', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/code/test/build/learn_service', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/code/test/src'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1705'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '1897'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '4003'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('JOURNAL_STREAM', '8:37892'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1871,unix/wuliu:/tmp/.ICE-unix/1871'), ('INVOCATION_ID', '0f1f5978a248456ea7c2b8da223c529b'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-9f1a6cd0ea1050e2.txt'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.9FCYB3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-a111bf15bc.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/test/build/learn_service'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.048025] (learn_service) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target service_server\x1b[0m\n'}
[0.048312] (learn_service) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target service_client\x1b[0m\n'}
[0.068371] (learn_service) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/service_server.dir/src/service_server.cpp.o\x1b[0m\n'}
[0.068609] (learn_service) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/service_client.dir/src/service_client.cpp.o\x1b[0m\n'}
[0.099534] (-) TimerEvent: {}
[0.199884] (-) TimerEvent: {}
[0.300230] (-) TimerEvent: {}
[0.400578] (-) TimerEvent: {}
[0.500931] (-) TimerEvent: {}
[0.601273] (-) TimerEvent: {}
[0.701615] (-) TimerEvent: {}
[0.802093] (-) TimerEvent: {}
[0.902618] (-) TimerEvent: {}
[1.002961] (-) TimerEvent: {}
[1.103312] (-) TimerEvent: {}
[1.203664] (-) TimerEvent: {}
[1.304045] (-) TimerEvent: {}
[1.404447] (-) TimerEvent: {}
[1.504787] (-) TimerEvent: {}
[1.605157] (-) TimerEvent: {}
[1.705489] (-) TimerEvent: {}
[1.805850] (-) TimerEvent: {}
[1.906238] (-) TimerEvent: {}
[1.939277] (learn_service) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/usr/include/c++/11/pstl/glue_algorithm_defs.h:13\x1b[m\x1b[K,\n'}
[1.939651] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/usr/include/c++/11/algorithm:74\x1b[m\x1b[K,\n'}
[1.939827] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:18\x1b[m\x1b[K,\n'}
[1.939972] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[1.940127] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[1.940316] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[1.940451] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:1\x1b[m\x1b[K:\n'}
[1.940575] (learn_service) StderrLine: {'line': b'/usr/include/c++/11/functional: In instantiation of \xe2\x80\x98\x1b[01m\x1b[Kstruct std::_Bind_check_arity<void (Service::*)(), Service*, const std::_Placeholder<1>&, const std::_Placeholder<2>&>\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.940720] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/functional:768:12:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Kstruct std::_Bind_helper<false, void (Service::*)(), Service*, const std::_Placeholder<1>&, const std::_Placeholder<2>&>\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.940842] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/functional:789:5:\x1b[m\x1b[K   required by substitution of \xe2\x80\x98\x1b[01m\x1b[Ktemplate<class _Func, class ... _BoundArgs> typename std::_Bind_helper<std::__is_socketlike<_Func>::value, _Func, _BoundArgs ...>::type std::bind(_Func&&, _BoundArgs&& ...) [with _Func = void (Service::*)(); _BoundArgs = {Service*, const std::_Placeholder<1>&, const std::_Placeholder<2>&}]\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.940916] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:27:22:\x1b[m\x1b[K   required from here\n'}
[1.940979] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/functional:756:21:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kstatic assertion failed: Wrong number of arguments for pointer-to-member\n'}
[1.941038] (learn_service) StderrLine: {'line': b'  755 |       static_assert(_Varargs::\x1b[01;31m\x1b[Kvalue\x1b[m\x1b[K\n'}
[1.941095] (learn_service) StderrLine: {'line': b'      |                               \x1b[01;31m\x1b[K~~~~~\x1b[m\x1b[K\n'}
[1.941162] (learn_service) StderrLine: {'line': b'  756 | \x1b[01;31m\x1b[K                    ? sizeof...(_BoundArgs) >= _Arity::value + 1\x1b[m\x1b[K\n'}
[1.941245] (learn_service) StderrLine: {'line': b'      |                     \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.941309] (learn_service) StderrLine: {'line': b'  757 | \x1b[01;31m\x1b[K                    : sizeof...(_BoundArgs) == _Arity::value + 1\x1b[m\x1b[K,\n'}
[1.941373] (learn_service) StderrLine: {'line': b'      |                     \x1b[01;31m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.941432] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/functional:756:21:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[K(false ? (3 >= (((long unsigned int)std::integral_constant<long unsigned int, 0>::value) + 1)) : (3 == (((long unsigned int)std::integral_constant<long unsigned int, 0>::value) + 1)))\x1b[m\x1b[K\xe2\x80\x99 evaluates to false\n'}
[1.944233] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:35:6:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno declaration matches \xe2\x80\x98\x1b[01m\x1b[Kvoid Service::add_two_ints_callback(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.944427] (learn_service) StderrLine: {'line': b'   35 | void \x1b[01;31m\x1b[KService\x1b[m\x1b[K::add_two_ints_callback(\n'}
[1.944540] (learn_service) StderrLine: {'line': b'      |      \x1b[01;31m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[1.944705] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:13:10:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate is: \xe2\x80\x98\x1b[01m\x1b[Kvoid Service::add_two_ints_callback()\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.944891] (learn_service) StderrLine: {'line': b'   13 |     void \x1b[01;36m\x1b[Kadd_two_ints_callback\x1b[m\x1b[K();\n'}
[1.945061] (learn_service) StderrLine: {'line': b'      |          \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.945176] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:6:7:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass Service\x1b[m\x1b[K\xe2\x80\x99 defined here\n'}
[1.945292] (learn_service) StderrLine: {'line': b'    6 | class \x1b[01;36m\x1b[KService\x1b[m\x1b[K : public rclcpp::Node\n'}
[1.945388] (learn_service) StderrLine: {'line': b'      |       \x1b[01;36m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[1.968493] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:27:6:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno declaration matches \xe2\x80\x98\x1b[01m\x1b[Kvoid ServiceClinet::send_request(int64_t, int64_t)\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.968739] (learn_service) StderrLine: {'line': b'   27 | void \x1b[01;31m\x1b[KServiceClinet\x1b[m\x1b[K::send_request(int64_t a, int64_t b)\n'}
[1.968874] (learn_service) StderrLine: {'line': b'      |      \x1b[01;31m\x1b[K^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.968979] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:13:10:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate is: \xe2\x80\x98\x1b[01m\x1b[Kvoid ServiceClinet::send_request()\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.969079] (learn_service) StderrLine: {'line': b'   13 |     void \x1b[01;36m\x1b[Ksend_request\x1b[m\x1b[K();\n'}
[1.969176] (learn_service) StderrLine: {'line': b'      |          \x1b[01;36m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.969271] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:6:7:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass ServiceClinet\x1b[m\x1b[K\xe2\x80\x99 defined here\n'}
[1.969379] (learn_service) StderrLine: {'line': b'    6 | class \x1b[01;36m\x1b[KServiceClinet\x1b[m\x1b[K : public rclcpp::Node\n'}
[1.969458] (learn_service) StderrLine: {'line': b'      |       \x1b[01;36m\x1b[K^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.970577] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.970645] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:82:30:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno matching function for call to \xe2\x80\x98\x1b[01m\x1b[KServiceClinet::send_request(int&, int&)\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.970689] (learn_service) StderrLine: {'line': b'   82 |     \x1b[01;31m\x1b[Kclient_node->send_request(a, b)\x1b[m\x1b[K;\n'}
[1.970731] (learn_service) StderrLine: {'line': b'      |     \x1b[01;31m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[1.970772] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:13:10:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Kvoid ServiceClinet::send_request()\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.970817] (learn_service) StderrLine: {'line': b'   13 |     void \x1b[01;36m\x1b[Ksend_request\x1b[m\x1b[K();\n'}
[1.970859] (learn_service) StderrLine: {'line': b'      |          \x1b[01;36m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.977865] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:13:10:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  candidate expects 0 arguments, 2 provided\n'}
[2.006357] (-) TimerEvent: {}
[2.106854] (-) TimerEvent: {}
[2.157642] (learn_service) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/service.hpp:36\x1b[m\x1b[K,\n'}
[2.158215] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:28\x1b[m\x1b[K,\n'}
[2.158349] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[2.158403] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[2.158453] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[2.158501] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[2.158584] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[2.158630] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[2.158674] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[2.158717] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[2.158758] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:1\x1b[m\x1b[K:\n'}
[2.158800] (learn_service) StderrLine: {'line': b'/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp: In instantiation of \xe2\x80\x98\x1b[01m\x1b[Kvoid rclcpp::AnyServiceCallback<ServiceT>::set(CallbackT&&) [with CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename std::enable_if<(! rclcpp::detail::can_be_nullptr<CallbackT>::value), int>::type <anonymous> = 0; ServiceT = example_interfaces::srv::AddTwoInts]\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.158846] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/create_service.hpp:43:27:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::create_service(std::shared_ptr<rclcpp::node_interfaces::NodeBaseInterface>, std::shared_ptr<rclcpp::node_interfaces::NodeServicesInterface>, const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.158900] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/node_impl.hpp:147:53:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::Node::create_service(const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.158959] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:25:73:\x1b[m\x1b[K   required from here\n'}
[2.159003] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp:103:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno match for \xe2\x80\x98\x1b[01m\x1b[Koperator=\x1b[m\x1b[K\xe2\x80\x99 (operand types are \xe2\x80\x98\x1b[01m\x1b[Kstd::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>\x1b[m\x1b[K\xe2\x80\x99)\n'}
[2.159059] (learn_service) StderrLine: {'line': b'  103 |       \x1b[01;31m\x1b[Kcallback_ = std::forward<CallbackT>(callback)\x1b[m\x1b[K;\n'}
[2.159104] (learn_service) StderrLine: {'line': b'      |       \x1b[01;31m\x1b[K~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.159147] (learn_service) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:28\x1b[m\x1b[K,\n'}
[2.159188] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[2.159229] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[2.159271] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[2.159311] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[2.159350] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[2.159390] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[2.159430] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[2.159471] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[2.159510] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[2.159550] (learn_service) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:1\x1b[m\x1b[K:\n'}
[2.159595] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1461:9:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Ktemplate<class _Tp> std::enable_if_t<((__exactly_once<std::variant<_Types>::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type> > && is_constructible_v<std::variant<_Types>::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>, _Tp>) && is_assignable_v<std::variant<_Types>::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>&, _Tp>), std::variant<_Types>&> std::variant<_Types>::operator=(_Tp&&) [with _Tp = _Tp; _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.159689] (learn_service) StderrLine: {'line': b' 1461 |         \x1b[01;36m\x1b[Koperator\x1b[m\x1b[K=(_Tp&& __rhs)\n'}
[2.159742] (learn_service) StderrLine: {'line': b'      |         \x1b[01;36m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[2.159788] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1461:9:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  template argument deduction/substitution failed:\n'}
[2.159838] (learn_service) StderrLine: {'line': b'/usr/include/c++/11/variant: In substitution of \xe2\x80\x98\x1b[01m\x1b[Ktemplate<class ... _Types> template<class _Tp, class> using __accepted_type = std::variant<_Types>::__to_type<__accepted_index<_Tp> > [with _Tp = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>&&; <template-parameter-2-2> = void; _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}]\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.159886] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1457:14:\x1b[m\x1b[K   required by substitution of \xe2\x80\x98\x1b[01m\x1b[Ktemplate<class _Tp> std::enable_if_t<((__exactly_once<std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type> > && is_constructible_v<std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>, _Tp>) && is_assignable_v<std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>&, _Tp>), std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >&> std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::operator=<_Tp>(_Tp&&) [with _Tp = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.159980] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp:103:17:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Kvoid rclcpp::AnyServiceCallback<ServiceT>::set(CallbackT&&) [with CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename std::enable_if<(! rclcpp::detail::can_be_nullptr<CallbackT>::value), int>::type <anonymous> = 0; ServiceT = example_interfaces::srv::AddTwoInts]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160027] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/create_service.hpp:43:27:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::create_service(std::shared_ptr<rclcpp::node_interfaces::NodeBaseInterface>, std::shared_ptr<rclcpp::node_interfaces::NodeServicesInterface>, const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160077] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/node_impl.hpp:147:53:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::Node::create_service(const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160126] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:25:73:\x1b[m\x1b[K   required from here\n'}
[2.160196] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1375:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno type named \xe2\x80\x98\x1b[01m\x1b[Ktype\x1b[m\x1b[K\xe2\x80\x99 in \xe2\x80\x98\x1b[01m\x1b[Kstruct std::enable_if<false, void>\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160248] (learn_service) StderrLine: {'line': b' 1375 |         using \x1b[01;31m\x1b[K__accepted_type\x1b[m\x1b[K = __to_type<__accepted_index<_Tp>>;\n'}
[2.160295] (learn_service) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.160338] (learn_service) StderrLine: {'line': b'/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp: In instantiation of \xe2\x80\x98\x1b[01m\x1b[Kvoid rclcpp::AnyServiceCallback<ServiceT>::set(CallbackT&&) [with CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename std::enable_if<(! rclcpp::detail::can_be_nullptr<CallbackT>::value), int>::type <anonymous> = 0; ServiceT = example_interfaces::srv::AddTwoInts]\x1b[m\x1b[K\xe2\x80\x99:\n'}
[2.160382] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/create_service.hpp:43:27:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::create_service(std::shared_ptr<rclcpp::node_interfaces::NodeBaseInterface>, std::shared_ptr<rclcpp::node_interfaces::NodeServicesInterface>, const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160425] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/node_impl.hpp:147:53:\x1b[m\x1b[K   required from \xe2\x80\x98\x1b[01m\x1b[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::Node::create_service(const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160467] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:25:73:\x1b[m\x1b[K   required from here\n'}
[2.160510] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1398:16:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Kstd::variant<_Types>& std::variant<_Types>::operator=(const std::variant<_Types>&) [with _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160568] (learn_service) StderrLine: {'line': b' 1398 |       variant& \x1b[01;36m\x1b[Koperator\x1b[m\x1b[K=(const variant&) = default;\n'}
[2.160612] (learn_service) StderrLine: {'line': b'      |                \x1b[01;36m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[2.160653] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1398:26:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  no known conversion for argument 1 from \xe2\x80\x98\x1b[01m\x1b[Kstd::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kconst std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >&\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160802] (learn_service) StderrLine: {'line': b' 1398 |       variant& operator=(\x1b[01;36m\x1b[Kconst variant&\x1b[m\x1b[K) = default;\n'}
[2.160847] (learn_service) StderrLine: {'line': b'      |                          \x1b[01;36m\x1b[K^~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[2.160891] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1399:16:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Kstd::variant<_Types>& std::variant<_Types>::operator=(std::variant<_Types>&&) [with _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}]\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.160937] (learn_service) StderrLine: {'line': b' 1399 |       variant& \x1b[01;36m\x1b[Koperator\x1b[m\x1b[K=(variant&&) = default;\n'}
[2.160978] (learn_service) StderrLine: {'line': b'      |                \x1b[01;36m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[2.161075] (learn_service) StderrLine: {'line': b'\x1b[01m\x1b[K/usr/include/c++/11/variant:1399:26:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  no known conversion for argument 1 from \xe2\x80\x98\x1b[01m\x1b[Kstd::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kstd::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >&&\x1b[m\x1b[K\xe2\x80\x99\n'}
[2.161172] (learn_service) StderrLine: {'line': b' 1399 |       variant& operator=(\x1b[01;36m\x1b[Kvariant&&\x1b[m\x1b[K) = default;\n'}
[2.161248] (learn_service) StderrLine: {'line': b'      |                          \x1b[01;36m\x1b[K^~~~~~~~~\x1b[m\x1b[K\n'}
[2.206983] (-) TimerEvent: {}
[2.307394] (-) TimerEvent: {}
[2.402201] (learn_service) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/service_server.dir/build.make:76\xef\xbc\x9aCMakeFiles/service_server.dir/src/service_server.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[2.402539] (learn_service) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:165\xef\xbc\x9aCMakeFiles/service_server.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[2.402654] (learn_service) StderrLine: {'line': b'gmake[1]: *** \xe6\xad\xa3\xe5\x9c\xa8\xe7\xad\x89\xe5\xbe\x85\xe6\x9c\xaa\xe5\xae\x8c\xe6\x88\x90\xe7\x9a\x84\xe4\xbb\xbb\xe5\x8a\xa1....\n'}
[2.407477] (-) TimerEvent: {}
[2.427364] (learn_service) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/service_client.dir/build.make:76\xef\xbc\x9aCMakeFiles/service_client.dir/src/service_client.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[2.427753] (learn_service) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:139\xef\xbc\x9aCMakeFiles/service_client.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[2.428119] (learn_service) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[2.430126] (learn_service) CommandEnded: {'returncode': 2}
[2.440641] (learn_service) JobEnded: {'identifier': 'learn_service', 'rc': 2}
[2.451234] (-) EventReactorShutdown: {}
