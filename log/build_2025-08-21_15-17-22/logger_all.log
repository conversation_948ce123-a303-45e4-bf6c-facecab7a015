[0.073s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.073s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7f571c7a3190>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f571c7a2d40>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f571c7a2d40>>)
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.188s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ros'
[0.200s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_service' with type 'ros.cmake' and name 'learn_service'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ros'
[0.201s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_topic' with type 'ros.ament_cmake' and name 'learn_topic'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.202s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.202s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.217s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.219s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 280 installed packages in /opt/ros/humble
[0.221s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_target' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_clean_cache' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_clean_first' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_force_configure' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'ament_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'catkin_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.259s] DEBUG:colcon.colcon_core.verb:Building package 'learn_service' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_service', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_service', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_service', 'symlink_install': False, 'test_result_base': None}
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_cache' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_first' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_force_configure' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'ament_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.259s] DEBUG:colcon.colcon_core.verb:Building package 'learn_topic' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_topic', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_topic', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_topic', 'symlink_install': False, 'test_result_base': None}
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_args' from command line to 'None'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target' from command line to 'None'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_cache' from command line to 'False'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_first' from command line to 'False'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_force_configure' from command line to 'False'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'ament_cmake_args' from command line to 'None'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_cmake_args' from command line to 'None'
[0.260s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.260s] DEBUG:colcon.colcon_core.verb:Building package 'pkg1' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/pkg1', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/pkg1', 'merge_install': False, 'path': '/home/<USER>/code/test/src/pkg1', 'symlink_install': False, 'test_result_base': None}
[0.260s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.261s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.261s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_service' with build type 'cmake'
[0.261s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_service'
[0.263s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.263s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.263s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.265s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_topic' with build type 'ament_cmake'
[0.265s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_topic'
[0.265s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.265s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.267s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/pkg1' with build type 'ament_cmake'
[0.267s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/pkg1'
[0.268s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.268s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.274s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[0.276s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.293s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.341s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[0.350s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
[0.351s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.352s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[0.360s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'pkg_config_path')
[0.361s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.ps1'
[0.361s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.dsv'
[0.362s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.sh'
[0.363s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
[0.365s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'pkg_config_path_multiarch')
[0.365s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.ps1'
[0.366s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.dsv'
[0.366s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.sh'
[0.366s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_service)
[0.368s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service' for CMake module files
[0.368s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service' for CMake config files
[0.368s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'cmake_prefix_path')
[0.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/cmake_prefix_path.ps1'
[0.369s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/cmake_prefix_path.dsv'
[0.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/cmake_prefix_path.sh'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/bin'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib/pkgconfig/learn_service.pc'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib/python3.10/site-packages'
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/bin'
[0.370s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.ps1'
[0.371s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv'
[0.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.sh'
[0.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.bash'
[0.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.zsh'
[0.373s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_service/share/colcon-core/packages/learn_service)
[0.376s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.376s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.377s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_topic)
[0.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake module files
[0.377s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[0.378s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake config files
[0.378s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_topic', 'cmake_prefix_path')
[0.378s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.ps1'
[0.378s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.dsv'
[0.378s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.sh'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/pkgconfig/learn_topic.pc'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/python3.10/site-packages'
[0.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[0.379s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.ps1'
[0.380s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv'
[0.380s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.sh'
[0.380s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.bash'
[0.381s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.zsh'
[0.381s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_topic/share/colcon-core/packages/learn_topic)
[0.381s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_topic)
[0.381s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake module files
[0.382s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake config files
[0.382s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_topic', 'cmake_prefix_path')
[0.382s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.ps1'
[0.383s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.dsv'
[0.383s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.sh'
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib'
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/pkgconfig/learn_topic.pc'
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/python3.10/site-packages'
[0.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[0.384s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.ps1'
[0.385s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv'
[0.386s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.sh'
[0.386s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.bash'
[0.386s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.zsh'
[0.387s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_topic/share/colcon-core/packages/learn_topic)
[0.387s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.388s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.388s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.389s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.389s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.389s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.390s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.391s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.391s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.391s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.391s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.392s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[0.392s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.393s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.393s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.393s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.393s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.394s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.395s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.395s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.395s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.395s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.396s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[0.396s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.396s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.396s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.396s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.401s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.401s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.401s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.411s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.412s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.ps1'
[0.412s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_ps1.py'
[0.414s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.ps1'
[0.414s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.sh'
[0.415s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_sh.py'
[0.415s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.sh'
[0.416s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.bash'
[0.416s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.bash'
[0.417s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.zsh'
[0.417s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.zsh'
