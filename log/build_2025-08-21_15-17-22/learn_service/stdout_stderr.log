[35m[1mConsolidate compiler generated dependencies of target service_server[0m
[35m[1mConsolidate compiler generated dependencies of target service_client[0m
[ 50%] Built target service_server
[100%] Built target service_client
-- Install configuration: ""
-- Up-to-date: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_server
-- Up-to-date: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_client
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/package_run_dependencies/learn_service
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/parent_prefix_path/learn_service
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.sh
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.dsv
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.bash
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.sh
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.zsh
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.dsv
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/packages/learn_service
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig.cmake
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig-version.cmake
-- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/package.xml
