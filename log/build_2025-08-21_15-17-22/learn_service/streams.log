[0.012s] Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[0.048s] [35m[1mConsolidate compiler generated dependencies of target service_server[0m
[0.048s] [35m[1mConsolidate compiler generated dependencies of target service_client[0m
[0.067s] [ 50%] Built target service_server
[0.067s] [100%] Built target service_client
[0.079s] Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[0.088s] Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
[0.095s] -- Install configuration: ""
[0.096s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_server
[0.096s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_client
[0.096s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/package_run_dependencies/learn_service
[0.096s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/parent_prefix_path/learn_service
[0.096s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.sh
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.dsv
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.sh
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.dsv
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.bash
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.sh
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.zsh
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.dsv
[0.097s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/packages/learn_service
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig.cmake
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig-version.cmake
[0.097s] -- Up-to-date: /home/<USER>/code/test/install/learn_service/share/learn_service/package.xml
[0.101s] Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
