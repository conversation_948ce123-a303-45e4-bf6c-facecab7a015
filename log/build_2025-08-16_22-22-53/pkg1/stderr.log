[01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:6:3:[m[K [01;31m[Kerror: [m[K‘[01m[Kaudo[m[K’ was not declared in this scope; did you mean ‘[01m[Kauto[m[K’?
    6 |   [01;31m[Kaudo[m[K node = rclcpp::Node::make_shared("helloworld");
      |   [01;31m[K^~~~[m[K
      |   [32m[Kauto[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:1[m[K:
[01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:15:[m[K [01;31m[Kerror: [m[K‘[01m[Knode[m[K’ was not declared in this scope
    8 |   RCLCPP_INFO([01;31m[Knode[m[K->get_logger(), "Hello World");
      |               [01;31m[K^~~~[m[K
[01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:3:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
    8 |   [01;31m[KRCLCPP_INFO[m[K(node->get_logger(), "Hello World");
      |   [01;31m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:3:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
    8 |   [01;31m[KRCLCPP_INFO[m[K(node->get_logger(), "Hello World");
      |   [01;31m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:3:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
    8 |   [01;31m[KRCLCPP_INFO[m[K(node->get_logger(), "Hello World");
      |   [01;31m[K^~~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/helloworld.dir/build.make:76：CMakeFiles/helloworld.dir/src/helloworld.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:137：CMakeFiles/helloworld.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
