[0.009s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.046s] [35m[1mConsolidate compiler generated dependencies of target helloworld[0m
[0.059s] [ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[1.866s] [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[1.866s] [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:6:3:[m[K [01;31m[Kerror: [m[K‘[01m[Kaudo[m[K’ was not declared in this scope; did you mean ‘[01m[Kauto[m[K’?
[1.866s]     6 |   [01;31m[Kaudo[m[K node = rclcpp::Node::make_shared("helloworld");
[1.867s]       |   [01;31m[K^~~~[m[K
[1.867s]       |   [32m[Kauto[m[K
[1.868s] In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
[1.868s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
[1.868s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
[1.868s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
[1.868s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
[1.868s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
[1.869s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
[1.869s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
[1.869s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
[1.869s]                  from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
[1.869s]                  from [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:1[m[K:
[1.869s] [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:15:[m[K [01;31m[Kerror: [m[K‘[01m[Knode[m[K’ was not declared in this scope
[1.869s]     8 |   RCLCPP_INFO([01;31m[Knode[m[K->get_logger(), "Hello World");
[1.869s]       |               [01;31m[K^~~~[m[K
[1.869s] [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:3:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[1.869s]     8 |   [01;31m[KRCLCPP_INFO[m[K(node->get_logger(), "Hello World");
[1.869s]       |   [01;31m[K^~~~~~~~~~~[m[K
[1.869s] [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:3:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[1.869s]     8 |   [01;31m[KRCLCPP_INFO[m[K(node->get_logger(), "Hello World");
[1.869s]       |   [01;31m[K^~~~~~~~~~~[m[K
[1.869s] [01m[K/home/<USER>/code/test/src/pkg1/src/helloworld.cpp:8:3:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
[1.869s]     8 |   [01;31m[KRCLCPP_INFO[m[K(node->get_logger(), "Hello World");
[1.870s]       |   [01;31m[K^~~~~~~~~~~[m[K
[2.206s] gmake[2]: *** [CMakeFiles/helloworld.dir/build.make:76：CMakeFiles/helloworld.dir/src/helloworld.cpp.o] 错误 1
[2.206s] gmake[1]: *** [CMakeFiles/Makefile2:137：CMakeFiles/helloworld.dir/all] 错误 2
[2.206s] gmake: *** [Makefile:146：all] 错误 2
[2.209s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
