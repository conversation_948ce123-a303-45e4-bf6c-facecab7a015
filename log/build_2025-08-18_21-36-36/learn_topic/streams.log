[0.009s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.056s] -- The C compiler identification is GNU 11.4.0
[0.103s] -- The CXX compiler identification is GNU 11.4.0
[0.110s] -- Detecting C compiler ABI info
[0.174s] -- Detecting C compiler ABI info - done
[0.179s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.179s] -- Detecting C compile features
[0.179s] -- Detecting C compile features - done
[0.181s] -- Detecting CXX compiler ABI info
[0.253s] -- Detecting CXX compiler ABI info - done
[0.258s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.258s] -- Detecting CXX compile features
[0.258s] -- Detecting CXX compile features - done
[0.261s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.375s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.444s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.472s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.475s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.481s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.489s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.500s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.529s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.531s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.612s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.634s] -- Found FastRTPS: /opt/ros/humble/include  
[0.670s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.675s] -- Looking for pthread.h
[0.740s] -- Looking for pthread.h - found
[0.740s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.807s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.808s] -- Found Threads: TRUE  
[0.863s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.921s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.921s] -- Configured cppcheck include dirs: 
[0.922s] -- Configured cppcheck exclude dirs and/or files: 
[0.922s] -- Added test 'lint_cmake' to check CMake code style
[0.923s] -- Added test 'uncrustify' to check C / C++ code style
[0.923s] -- Configured uncrustify additional arguments: 
[0.924s] -- Added test 'xmllint' to check XML markup files
[0.925s] -- Configuring done
[0.936s] -- Generating done
[0.940s] -- Build files have been written to: /home/<USER>/code/test/build/learn_topic
[0.953s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.955s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[1.001s] [ 50%] [32mBuilding CXX object CMakeFiles/pub_hello_world.dir/src/pub_hello_world.cpp.o[0m
[4.348s] [100%] [32m[1mLinking CXX executable pub_hello_world[0m
[4.488s] [100%] Built target pub_hello_world
[4.499s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[4.500s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[4.506s] -- Install configuration: ""
[4.506s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[4.508s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world" to ""
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[4.508s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[4.509s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[4.511s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
