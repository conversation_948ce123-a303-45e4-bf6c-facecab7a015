[0.070s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.071s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7fdb79f372e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fdb79f36e90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fdb79f36e90>>)
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.183s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.183s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_topic' with type 'ros.ament_cmake' and name 'learn_topic'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.210s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/code/test/install/pkg1' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.210s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/code/test/install/learn_topic' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.210s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/code/test/install/learn_node' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.210s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/pkg1' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.210s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/learn_topic' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.210s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/learn_node' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.211s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 0 installed packages in /home/<USER>/code/test/install
[0.212s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 272 installed packages in /opt/ros/humble
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_cache' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_first' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_force_configure' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'ament_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.248s] DEBUG:colcon.colcon_core.verb:Building package 'learn_topic' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_topic', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_topic', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_topic', 'symlink_install': False, 'test_result_base': None}
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_cache' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_first' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_force_configure' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'ament_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.249s] DEBUG:colcon.colcon_core.verb:Building package 'pkg1' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/pkg1', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/pkg1', 'merge_install': False, 'path': '/home/<USER>/code/test/src/pkg1', 'symlink_install': False, 'test_result_base': None}
[0.249s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.249s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.250s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_topic' with build type 'ament_cmake'
[0.250s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_topic'
[0.251s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.251s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.251s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.254s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/pkg1' with build type 'ament_cmake'
[0.254s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/pkg1'
[0.254s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.254s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.259s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.262s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[1.203s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[1.205s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[1.209s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[1.210s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[3.610s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[3.622s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[3.632s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[3.633s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[3.635s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[3.636s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[3.636s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[3.636s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[3.637s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[3.637s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[3.638s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[3.638s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[3.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[3.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[3.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[3.639s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[3.640s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[3.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[3.642s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[3.642s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[3.643s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[3.644s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[3.644s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[3.645s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[3.645s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[3.645s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[3.646s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[3.646s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[3.647s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[3.647s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[3.647s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[3.647s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[3.647s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[3.648s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[3.649s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[3.649s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[3.650s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[3.650s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[3.651s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
