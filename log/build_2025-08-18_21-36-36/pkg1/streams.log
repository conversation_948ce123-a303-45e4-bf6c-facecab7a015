[0.008s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[0.063s] -- The C compiler identification is GNU 11.4.0
[0.110s] -- The CXX compiler identification is GNU 11.4.0
[0.116s] -- Detecting C compiler ABI info
[0.179s] -- Detecting C compiler ABI info - done
[0.184s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.184s] -- Detecting C compile features
[0.185s] -- Detecting C compile features - done
[0.187s] -- Detecting CXX compiler ABI info
[0.260s] -- Detecting CXX compiler ABI info - done
[0.265s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.265s] -- Detecting CXX compile features
[0.266s] -- Detecting CXX compile features - done
[0.267s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.384s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.451s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.479s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.483s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.488s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.497s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.508s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.537s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.539s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.617s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.639s] -- Found FastRTPS: /opt/ros/humble/include  
[0.675s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.680s] -- Looking for pthread.h
[0.746s] -- Looking for pthread.h - found
[0.747s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.812s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.813s] -- Found Threads: TRUE  
[0.867s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.929s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.929s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/code/test/src/pkg1/include>
[0.929s] -- Configured cppcheck exclude dirs and/or files: 
[0.930s] -- Added test 'lint_cmake' to check CMake code style
[0.931s] -- Added test 'uncrustify' to check C / C++ code style
[0.931s] -- Configured uncrustify additional arguments: 
[0.931s] -- Added test 'xmllint' to check XML markup files
[0.932s] -- Configuring done
[0.942s] -- Generating done
[0.946s] -- Build files have been written to: /home/<USER>/code/test/build/pkg1
[0.955s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[0.956s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[1.001s] [ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[3.230s] [100%] [32m[1mLinking CXX executable helloworld[0m
[3.344s] [100%] Built target helloworld
[3.356s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[3.367s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[3.373s] -- Install configuration: ""
[3.374s] -- Installing: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
[3.374s] -- Set runtime path of "/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld" to ""
[3.374s] -- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
[3.374s] -- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
[3.374s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
[3.374s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
[3.375s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
[3.375s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
[3.375s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
[3.375s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
[3.376s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
[3.376s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
[3.376s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
[3.376s] -- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
[3.377s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
[3.377s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
[3.377s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml
[3.379s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
