[0.011s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.047s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.161s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.187s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.188s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.194s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.201s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.212s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.240s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.241s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.332s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.376s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.403s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.414s] -- Found OpenCV: /usr (found version "4.5.4") 
[0.416s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.469s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.470s] -- Configured cppcheck include dirs: 
[0.470s] -- Configured cppcheck exclude dirs and/or files: 
[0.470s] -- Added test 'lint_cmake' to check CMake code style
[0.471s] -- Added test 'uncrustify' to check C / C++ code style
[0.471s] -- Configured uncrustify additional arguments: 
[0.472s] -- Added test 'xmllint' to check XML markup files
[0.472s] -- Configuring done
[0.488s] -- Generating done
[0.493s] -- Build files have been written to: /home/<USER>/code/test/build/learn_topic
[0.519s] [35m[1mConsolidate compiler generated dependencies of target pub_hello_world[0m
[0.519s] [35m[1mConsolidate compiler generated dependencies of target sub_hello_world[0m
[0.528s] [ 16%] [32mBuilding CXX object CMakeFiles/pub_img.dir/src/pub_img.cpp.o[0m
[0.537s] [ 66%] [32mBuilding CXX object CMakeFiles/sub_hello_world.dir/src/sub_hello_world.cpp.o[0m
[0.537s] [ 66%] Built target pub_hello_world
[5.135s] [ 83%] [32m[1mLinking CXX executable pub_img[0m
[5.634s] [ 83%] Built target pub_img
[6.638s] [100%] [32m[1mLinking CXX executable sub_hello_world[0m
[6.900s] [100%] Built target sub_hello_world
[6.911s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[6.913s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[6.919s] -- Install configuration: ""
[6.920s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[6.920s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world
[6.925s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world" to ""
[6.925s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img
[6.927s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img" to ""
[6.927s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[6.927s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[6.927s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[6.927s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[6.927s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[6.927s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[6.927s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[6.927s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[6.927s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[6.927s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[6.928s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[6.928s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[6.928s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[6.928s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[6.928s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[6.930s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
