[0.009s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.060s] [100%] Built target pub_hello_world
[0.070s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.080s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[0.087s] -- Install configuration: ""
[0.087s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[0.087s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[0.088s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[0.089s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[0.089s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[0.089s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[0.089s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[0.089s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[0.090s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
