[0.076s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'learn_service']
[0.076s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['learn_service'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7fbf7ab372b0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fbf7ab36e60>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fbf7ab36e60>>)
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.190s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_service' with type 'ros.cmake' and name 'learn_service'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_topic' with type 'ros.ament_cmake' and name 'learn_topic'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.218s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'learn_topic' in 'src/learn_topic'
[0.218s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'pkg1' in 'src/pkg1'
[0.218s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.218s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.220s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 280 installed packages in /opt/ros/humble
[0.222s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_args' from command line to 'None'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_target' from command line to 'None'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_clean_cache' from command line to 'False'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_clean_first' from command line to 'False'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_force_configure' from command line to 'False'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'ament_cmake_args' from command line to 'None'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'catkin_cmake_args' from command line to 'None'
[0.256s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.256s] DEBUG:colcon.colcon_core.verb:Building package 'learn_service' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_service', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_service', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_service', 'symlink_install': False, 'test_result_base': None}
[0.257s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.257s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.258s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_service' with build type 'cmake'
[0.258s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_service'
[0.259s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.260s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.260s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.267s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[2.739s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_service' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[2.739s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'pkg_config_path')
[2.740s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.ps1'
[2.740s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.dsv'
[2.740s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.sh'
[2.743s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'pkg_config_path_multiarch')
[2.743s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.ps1'
[2.743s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.dsv'
[2.744s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.sh'
[2.744s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_service)
[2.746s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service' for CMake module files
[2.746s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service' for CMake config files
[2.746s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/bin'
[2.746s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib/pkgconfig/learn_service.pc'
[2.747s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib/python3.10/site-packages'
[2.747s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/bin'
[2.747s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.ps1'
[2.747s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv'
[2.748s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.sh'
[2.748s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.bash'
[2.749s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.zsh'
[2.749s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_service/share/colcon-core/packages/learn_service)
[2.759s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.760s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.760s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[2.760s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.764s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.764s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.764s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.774s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.774s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.ps1'
[2.776s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_ps1.py'
[2.777s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.ps1'
[2.777s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.sh'
[2.778s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_sh.py'
[2.778s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.sh'
[2.779s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.bash'
[2.780s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.bash'
[2.780s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.zsh'
[2.781s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.zsh'
