[35m[1mConsolidate compiler generated dependencies of target helloworld[0m
[100%] Built target helloworld
-- Install configuration: ""
-- Installing: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
-- Set runtime path of "/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld" to ""
-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml
