[0.000000] (-) TimerEvent: {}
[0.000176] (learn_topic) JobQueued: {'identifier': 'learn_topic', 'dependencies': OrderedDict()}
[0.000238] (pkg1) JobQueued: {'identifier': 'pkg1', 'dependencies': OrderedDict()}
[0.000346] (learn_topic) JobStarted: {'identifier': 'learn_topic'}
[0.004202] (pkg1) JobStarted: {'identifier': 'pkg1'}
[0.007384] (learn_topic) JobProgress: {'identifier': 'learn_topic', 'progress': 'cmake'}
[0.008030] (learn_topic) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/code/test/src/learn_topic', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic'], 'cwd': '/home/<USER>/code/test/build/learn_topic', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1716'), ('SYSTEMD_EXEC_PID', '1950'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2764'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/test/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('JOURNAL_STREAM', '8:38992'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1920,unix/wuliu:/tmp/.ICE-unix/1920'), ('INVOCATION_ID', '839e06bb6d704d2280146bee28099142'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.0V8FB3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-a111bf15bc.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/test/build/learn_topic'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble')]), 'shell': False}
[0.010440] (pkg1) JobProgress: {'identifier': 'pkg1', 'progress': 'cmake'}
[0.011284] (pkg1) JobProgress: {'identifier': 'pkg1', 'progress': 'build'}
[0.012237] (pkg1) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/test/build/pkg1', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/code/test/build/pkg1', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1716'), ('SYSTEMD_EXEC_PID', '1950'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2764'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/test/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('JOURNAL_STREAM', '8:38992'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1920,unix/wuliu:/tmp/.ICE-unix/1920'), ('INVOCATION_ID', '839e06bb6d704d2280146bee28099142'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.0V8FB3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-a111bf15bc.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/test/build/pkg1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble')]), 'shell': False}
[0.020353] (learn_topic) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.050070] (pkg1) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target helloworld\x1b[0m\n'}
[0.069519] (pkg1) StdoutLine: {'line': b'[100%] Built target helloworld\n'}
[0.079948] (pkg1) CommandEnded: {'returncode': 0}
[0.080636] (pkg1) JobProgress: {'identifier': 'pkg1', 'progress': 'install'}
[0.090083] (pkg1) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/test/build/pkg1'], 'cwd': '/home/<USER>/code/test/build/pkg1', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1716'), ('SYSTEMD_EXEC_PID', '1950'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2764'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/test/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('JOURNAL_STREAM', '8:38992'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1920,unix/wuliu:/tmp/.ICE-unix/1920'), ('INVOCATION_ID', '839e06bb6d704d2280146bee28099142'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.0V8FB3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-a111bf15bc.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/test/build/pkg1'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble')]), 'shell': False}
[0.096782] (pkg1) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.096945] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld\n'}
[0.097556] (pkg1) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld" to ""\n'}
[0.097844] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1\n'}
[0.098013] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1\n'}
[0.098294] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh\n'}
[0.098453] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv\n'}
[0.098605] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh\n'}
[0.098672] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv\n'}
[0.098719] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash\n'}
[0.098777] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh\n'}
[0.098825] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh\n'}
[0.098885] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv\n'}
[0.099009] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv\n'}
[0.099170] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1\n'}
[0.099221] (-) TimerEvent: {}
[0.099350] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake\n'}
[0.099443] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake\n'}
[0.099570] (pkg1) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml\n'}
[0.101209] (pkg1) CommandEnded: {'returncode': 0}
[0.113469] (learn_topic) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.113899] (pkg1) JobEnded: {'identifier': 'pkg1', 'rc': 0}
[0.133815] (learn_topic) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.135644] (learn_topic) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.139648] (learn_topic) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.146775] (learn_topic) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.155629] (learn_topic) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.177744] (learn_topic) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.178391] (learn_topic) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.199386] (-) TimerEvent: {}
[0.265173] (learn_topic) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.297957] (learn_topic) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.299440] (-) TimerEvent: {}
[0.318825] (learn_topic) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[0.329751] (learn_topic) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[0.381659] (learn_topic) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[0.382226] (learn_topic) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[0.382388] (learn_topic) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[0.382718] (learn_topic) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.383468] (learn_topic) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[0.383554] (learn_topic) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[0.383699] (learn_topic) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.384206] (learn_topic) StdoutLine: {'line': b'-- Configuring done\n'}
[0.399577] (-) TimerEvent: {}
[0.402668] (learn_topic) StdoutLine: {'line': b'-- Generating done\n'}
[0.408678] (learn_topic) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/code/test/build/learn_topic\n'}
[0.416819] (learn_topic) CommandEnded: {'returncode': 0}
[0.417413] (learn_topic) JobProgress: {'identifier': 'learn_topic', 'progress': 'build'}
[0.418020] (learn_topic) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/code/test/build/learn_topic', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/code/test/build/learn_topic', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1716'), ('SYSTEMD_EXEC_PID', '1950'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2764'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/test/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('JOURNAL_STREAM', '8:38992'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1920,unix/wuliu:/tmp/.ICE-unix/1920'), ('INVOCATION_ID', '839e06bb6d704d2280146bee28099142'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.0V8FB3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-a111bf15bc.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/test/build/learn_topic'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble')]), 'shell': False}
[0.466987] (learn_topic) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/pub_img.dir/src/pub_img.cpp.o\x1b[0m\n'}
[0.467215] (learn_topic) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/pub_hello_world.dir/src/pub_hello_world.cpp.o\x1b[0m\n'}
[0.467321] (learn_topic) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/sub_img.dir/src/sub_img.cpp.o\x1b[0m\n'}
[0.467414] (learn_topic) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/sub_hello_world.dir/src/sub_hello_world.cpp.o\x1b[0m\n'}
[0.499721] (-) TimerEvent: {}
[0.600312] (-) TimerEvent: {}
[0.700633] (-) TimerEvent: {}
[0.800930] (-) TimerEvent: {}
[0.901285] (-) TimerEvent: {}
[1.001640] (-) TimerEvent: {}
[1.101995] (-) TimerEvent: {}
[1.202353] (-) TimerEvent: {}
[1.302764] (-) TimerEvent: {}
[1.403120] (-) TimerEvent: {}
[1.503483] (-) TimerEvent: {}
[1.603838] (-) TimerEvent: {}
[1.704162] (-) TimerEvent: {}
[1.804491] (-) TimerEvent: {}
[1.904846] (-) TimerEvent: {}
[2.005202] (-) TimerEvent: {}
[2.105550] (-) TimerEvent: {}
[2.205912] (-) TimerEvent: {}
[2.306263] (-) TimerEvent: {}
[2.406641] (-) TimerEvent: {}
[2.506995] (-) TimerEvent: {}
[2.607354] (-) TimerEvent: {}
[2.707709] (-) TimerEvent: {}
[2.808072] (-) TimerEvent: {}
[2.908423] (-) TimerEvent: {}
[3.008776] (-) TimerEvent: {}
[3.109128] (-) TimerEvent: {}
[3.209471] (-) TimerEvent: {}
[3.309811] (-) TimerEvent: {}
[3.410187] (-) TimerEvent: {}
[3.510511] (-) TimerEvent: {}
[3.610834] (-) TimerEvent: {}
[3.711169] (-) TimerEvent: {}
[3.811545] (-) TimerEvent: {}
[3.911934] (-) TimerEvent: {}
[3.913253] (learn_topic) StdoutLine: {'line': b'[ 62%] \x1b[32m\x1b[1mLinking CXX executable pub_hello_world\x1b[0m\n'}
[4.012059] (-) TimerEvent: {}
[4.063641] (learn_topic) StdoutLine: {'line': b'[ 62%] Built target pub_hello_world\n'}
[4.112183] (-) TimerEvent: {}
[4.212531] (-) TimerEvent: {}
[4.312926] (-) TimerEvent: {}
[4.413250] (-) TimerEvent: {}
[4.513521] (-) TimerEvent: {}
[4.613808] (-) TimerEvent: {}
[4.714139] (-) TimerEvent: {}
[4.814405] (-) TimerEvent: {}
[4.914737] (-) TimerEvent: {}
[5.015033] (-) TimerEvent: {}
[5.115384] (-) TimerEvent: {}
[5.215716] (-) TimerEvent: {}
[5.305928] (learn_topic) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX executable pub_img\x1b[0m\n'}
[5.315848] (-) TimerEvent: {}
[5.416188] (-) TimerEvent: {}
[5.516538] (-) TimerEvent: {}
[5.616858] (-) TimerEvent: {}
[5.717164] (-) TimerEvent: {}
[5.749604] (learn_topic) StdoutLine: {'line': b'[ 75%] Built target pub_img\n'}
[5.817254] (-) TimerEvent: {}
[5.917530] (-) TimerEvent: {}
[6.017847] (-) TimerEvent: {}
[6.118135] (-) TimerEvent: {}
[6.218389] (-) TimerEvent: {}
[6.318730] (-) TimerEvent: {}
[6.419054] (-) TimerEvent: {}
[6.519394] (-) TimerEvent: {}
[6.619727] (-) TimerEvent: {}
[6.720063] (-) TimerEvent: {}
[6.724978] (learn_topic) StdoutLine: {'line': b'[ 87%] \x1b[32m\x1b[1mLinking CXX executable sub_hello_world\x1b[0m\n'}
[6.820987] (-) TimerEvent: {}
[6.921349] (-) TimerEvent: {}
[7.005594] (learn_topic) StdoutLine: {'line': b'[ 87%] Built target sub_hello_world\n'}
[7.021460] (-) TimerEvent: {}
[7.121781] (-) TimerEvent: {}
[7.222125] (-) TimerEvent: {}
[7.322488] (-) TimerEvent: {}
[7.422818] (-) TimerEvent: {}
[7.523161] (-) TimerEvent: {}
[7.623519] (-) TimerEvent: {}
[7.679513] (learn_topic) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable sub_img\x1b[0m\n'}
[7.723645] (-) TimerEvent: {}
[7.823982] (-) TimerEvent: {}
[7.924329] (-) TimerEvent: {}
[8.024728] (-) TimerEvent: {}
[8.125065] (-) TimerEvent: {}
[8.222718] (learn_topic) StdoutLine: {'line': b'[100%] Built target sub_img\n'}
[8.225137] (-) TimerEvent: {}
[8.233605] (learn_topic) CommandEnded: {'returncode': 0}
[8.234522] (learn_topic) JobProgress: {'identifier': 'learn_topic', 'progress': 'install'}
[8.234815] (learn_topic) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/code/test/build/learn_topic'], 'cwd': '/home/<USER>/code/test/build/learn_topic', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'wuliu'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1716'), ('SYSTEMD_EXEC_PID', '1950'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2764'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/code/test/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'wuliu'), ('JOURNAL_STREAM', '8:38992'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'wuliu'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/wuliu:@/tmp/.ICE-unix/1920,unix/wuliu:/tmp/.ICE-unix/1920'), ('INVOCATION_ID', '839e06bb6d704d2280146bee28099142'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.0V8FB3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-a111bf15bc.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/code/test/build/learn_topic'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/code/test/install/pkg1:/home/<USER>/code/test/install/learn_topic:/opt/ros/humble')]), 'shell': False}
[8.241613] (learn_topic) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[8.241841] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world\n'}
[8.243170] (learn_topic) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world" to ""\n'}
[8.243263] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world\n'}
[8.247847] (learn_topic) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world" to ""\n'}
[8.248040] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img\n'}
[8.249205] (learn_topic) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img" to ""\n'}
[8.249323] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_img\n'}
[8.253080] (learn_topic) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_img" to ""\n'}
[8.253229] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic\n'}
[8.253388] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic\n'}
[8.253555] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh\n'}
[8.253685] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv\n'}
[8.253811] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh\n'}
[8.253941] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv\n'}
[8.254056] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash\n'}
[8.254171] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh\n'}
[8.254287] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh\n'}
[8.254411] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv\n'}
[8.254534] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv\n'}
[8.254673] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic\n'}
[8.254819] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake\n'}
[8.254926] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake\n'}
[8.255042] (learn_topic) StdoutLine: {'line': b'-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml\n'}
[8.257099] (learn_topic) CommandEnded: {'returncode': 0}
[8.265889] (learn_topic) JobEnded: {'identifier': 'learn_topic', 'rc': 0}
[8.266543] (-) EventReactorShutdown: {}
