[0.010s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.020s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.113s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.134s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.135s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.139s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.146s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.155s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.178s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.178s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.265s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.298s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.319s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.329s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.381s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.382s] -- Configured cppcheck include dirs: 
[0.382s] -- Configured cppcheck exclude dirs and/or files: 
[0.382s] -- Added test 'lint_cmake' to check CMake code style
[0.383s] -- Added test 'uncrustify' to check C / C++ code style
[0.383s] -- Configured uncrustify additional arguments: 
[0.383s] -- Added test 'xmllint' to check XML markup files
[0.384s] -- Configuring done
[0.402s] -- Generating done
[0.408s] -- Build files have been written to: /home/<USER>/code/test/build/learn_topic
[0.417s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.418s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.467s] [ 50%] [32mBuilding CXX object CMakeFiles/pub_img.dir/src/pub_img.cpp.o[0m
[0.467s] [ 50%] [32mBuilding CXX object CMakeFiles/pub_hello_world.dir/src/pub_hello_world.cpp.o[0m
[0.467s] [ 50%] [32mBuilding CXX object CMakeFiles/sub_img.dir/src/sub_img.cpp.o[0m
[0.467s] [ 50%] [32mBuilding CXX object CMakeFiles/sub_hello_world.dir/src/sub_hello_world.cpp.o[0m
[3.913s] [ 62%] [32m[1mLinking CXX executable pub_hello_world[0m
[4.063s] [ 62%] Built target pub_hello_world
[5.306s] [ 75%] [32m[1mLinking CXX executable pub_img[0m
[5.749s] [ 75%] Built target pub_img
[6.725s] [ 87%] [32m[1mLinking CXX executable sub_hello_world[0m
[7.005s] [ 87%] Built target sub_hello_world
[7.679s] [100%] [32m[1mLinking CXX executable sub_img[0m
[8.222s] [100%] Built target sub_img
[8.233s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[8.235s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[8.241s] -- Install configuration: ""
[8.242s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[8.243s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world" to ""
[8.243s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world
[8.248s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world" to ""
[8.248s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img
[8.249s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img" to ""
[8.249s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_img
[8.253s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_img" to ""
[8.253s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[8.253s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[8.253s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[8.253s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[8.253s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[8.254s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[8.255s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[8.255s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[8.257s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
