[0.073s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.073s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7ffb35c37220>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7ffb35c36dd0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7ffb35c36dd0>>)
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.187s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.188s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.188s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ros'
[0.200s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_topic' with type 'ros.ament_cmake' and name 'learn_topic'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.201s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.201s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.201s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.201s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.201s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.201s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.215s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.215s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.215s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/code/test/install/pkg1' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.216s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/pkg1' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.216s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/code/test/install
[0.217s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 273 installed packages in /opt/ros/humble
[0.219s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.258s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_args' from command line to 'None'
[0.258s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_cache' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_first' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_force_configure' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'ament_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.259s] DEBUG:colcon.colcon_core.verb:Building package 'learn_topic' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_topic', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_topic', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_topic', 'symlink_install': False, 'test_result_base': None}
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_cache' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_first' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_force_configure' from command line to 'False'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'ament_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_cmake_args' from command line to 'None'
[0.259s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.259s] DEBUG:colcon.colcon_core.verb:Building package 'pkg1' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/pkg1', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/pkg1', 'merge_install': False, 'path': '/home/<USER>/code/test/src/pkg1', 'symlink_install': False, 'test_result_base': None}
[0.259s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.260s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.260s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_topic' with build type 'ament_cmake'
[0.260s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_topic'
[0.262s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.262s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.262s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.265s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/pkg1' with build type 'ament_cmake'
[0.265s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/pkg1'
[0.265s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.265s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.271s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.275s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.341s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.351s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.362s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.362s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.364s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.365s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.365s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.365s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.366s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.367s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.367s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.368s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.368s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.369s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.369s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[0.370s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.370s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.371s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.371s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.371s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.373s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.374s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.374s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[0.678s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.679s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[8.495s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[8.496s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[8.518s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_topic)
[8.518s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake module files
[8.518s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[8.519s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake config files
[8.519s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_topic', 'cmake_prefix_path')
[8.519s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.ps1'
[8.520s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.dsv'
[8.520s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.sh'
[8.520s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib'
[8.520s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[8.520s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/pkgconfig/learn_topic.pc'
[8.521s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/python3.10/site-packages'
[8.521s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[8.521s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.ps1'
[8.521s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv'
[8.521s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.sh'
[8.522s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.bash'
[8.522s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.zsh'
[8.522s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_topic/share/colcon-core/packages/learn_topic)
[8.523s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_topic)
[8.523s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake module files
[8.523s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake config files
[8.523s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_topic', 'cmake_prefix_path')
[8.523s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.ps1'
[8.524s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.dsv'
[8.524s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.sh'
[8.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib'
[8.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[8.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/pkgconfig/learn_topic.pc'
[8.524s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/python3.10/site-packages'
[8.525s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[8.525s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.ps1'
[8.525s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv'
[8.525s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.sh'
[8.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.bash'
[8.526s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.zsh'
[8.526s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_topic/share/colcon-core/packages/learn_topic)
[8.526s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[8.527s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[8.527s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[8.527s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[8.531s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[8.531s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[8.531s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[8.542s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[8.542s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.ps1'
[8.543s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_ps1.py'
[8.544s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.ps1'
[8.545s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.sh'
[8.545s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_sh.py'
[8.546s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.sh'
[8.546s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.bash'
[8.547s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.bash'
[8.547s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.zsh'
[8.548s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.zsh'
