[0.010s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.049s] [35m[1mConsolidate compiler generated dependencies of target helloworld[0m
[0.068s] [ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[2.256s] [100%] [32m[1mLinking CXX executable helloworld[0m
[2.347s] [100%] Built target helloworld
[2.358s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[2.369s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[2.375s] -- Install configuration: ""
[2.375s] -- Installing: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
[2.376s] -- Set runtime path of "/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld" to ""
[2.376s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
[2.376s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
[2.376s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
[2.377s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
[2.377s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml
[2.378s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
