[0.009s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.042s] [35m[1mConsolidate compiler generated dependencies of target helloworld[0m
[0.060s] [100%] Built target helloworld
[0.081s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.083s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.090s] -- Install configuration: ""
[0.090s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
[0.090s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
[0.090s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
[0.090s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
[0.091s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
[0.091s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
[0.092s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
[0.092s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
[0.092s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
[0.092s] -- Up-to-date: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml
[0.105s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
