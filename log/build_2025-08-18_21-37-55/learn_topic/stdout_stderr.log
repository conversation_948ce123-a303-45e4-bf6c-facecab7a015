[35m[1mConsolidate compiler generated dependencies of target pub_hello_world[0m
[100%] Built target pub_hello_world
-- Install configuration: ""
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
-- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
-- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
