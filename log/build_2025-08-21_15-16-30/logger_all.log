[0.074s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.074s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7f8075207190>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f8075206d40>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f8075206d40>>)
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.190s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_service) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_service' with type 'ros.cmake' and name 'learn_service'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_topic' with type 'ros.ament_cmake' and name 'learn_topic'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.205s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.205s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.220s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/code/test/install/pkg1' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.220s] WARNING:colcon.colcon_ros.prefix_path.ament:The path '/home/<USER>/code/test/install/learn_topic' in the environment variable AMENT_PREFIX_PATH doesn't exist
[0.220s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/pkg1' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.220s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/learn_topic' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.221s] WARNING:colcon.colcon_ros.prefix_path.catkin:The path '/home/<USER>/code/test/install/learn_service' in the environment variable CMAKE_PREFIX_PATH doesn't exist
[0.221s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 0 installed packages in /home/<USER>/code/test/install
[0.222s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 280 installed packages in /opt/ros/humble
[0.224s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'learn_service' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'learn_service' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_service', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_service', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_service', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'learn_topic' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_topic', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_topic', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_topic', 'symlink_install': False, 'test_result_base': None}
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'pkg1' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/pkg1', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/pkg1', 'merge_install': False, 'path': '/home/<USER>/code/test/src/pkg1', 'symlink_install': False, 'test_result_base': None}
[0.266s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.267s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.267s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_service' with build type 'cmake'
[0.267s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_service'
[0.268s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.268s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.269s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.271s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_topic' with build type 'ament_cmake'
[0.271s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_topic'
[0.271s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.271s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.274s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/pkg1' with build type 'ament_cmake'
[0.274s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/pkg1'
[0.274s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.274s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.279s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_service -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_service
[0.282s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.284s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[1.286s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[1.288s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[1.312s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_service -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_service
[1.313s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[1.336s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[1.337s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[4.211s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[4.223s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[4.238s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[4.239s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[4.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[4.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[4.241s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[4.242s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[4.242s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[4.242s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[4.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[4.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[4.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[4.243s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[4.244s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[4.244s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[4.244s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[4.245s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[4.245s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[4.246s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[4.246s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[4.247s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[4.247s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[4.247s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[4.247s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[4.248s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[4.248s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[4.248s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[4.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[4.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[4.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[4.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[4.249s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[4.250s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[4.250s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[4.250s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[4.251s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[4.251s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[4.251s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[4.874s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[4.876s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
[4.889s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
[4.890s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'pkg_config_path')
[4.890s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.ps1'
[4.891s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.dsv'
[4.891s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path.sh'
[4.893s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'pkg_config_path_multiarch')
[4.893s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.ps1'
[4.893s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.dsv'
[4.894s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/pkg_config_path_multiarch.sh'
[4.894s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_service)
[4.894s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service' for CMake module files
[4.895s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service' for CMake config files
[4.895s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_service', 'cmake_prefix_path')
[4.895s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/cmake_prefix_path.ps1'
[4.895s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/cmake_prefix_path.dsv'
[4.895s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_service/share/learn_service/hook/cmake_prefix_path.sh'
[4.896s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib'
[4.896s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/bin'
[4.896s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib/pkgconfig/learn_service.pc'
[4.896s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/lib/python3.10/site-packages'
[4.896s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_service/bin'
[4.897s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.ps1'
[4.897s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv'
[4.897s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.sh'
[4.898s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.bash'
[4.898s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_service/share/learn_service/package.zsh'
[4.898s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_service/share/colcon-core/packages/learn_service)
