[0.099s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.099s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7f26d2c37310>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f26d2c36ec0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f26d2c36ec0>>)
[0.262s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.263s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extensions ['ignore', 'ignore_ament_install']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extension 'ignore'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extension 'ignore_ament_install'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extensions ['colcon_pkg']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extension 'colcon_pkg'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extensions ['colcon_meta']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extension 'colcon_meta'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extensions ['ros']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_node) by extension 'ros'
[0.275s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_node' with type 'ros.ament_cmake' and name 'learn_node'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.276s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.276s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.276s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.276s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.276s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.276s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.295s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 272 installed packages in /opt/ros/humble
[0.297s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'cmake_target' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'cmake_clean_cache' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'cmake_clean_first' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'cmake_force_configure' from command line to 'False'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'ament_cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'catkin_cmake_args' from command line to 'None'
[0.334s] Level 5:colcon.colcon_core.verb:set package 'learn_node' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.334s] DEBUG:colcon.colcon_core.verb:Building package 'learn_node' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_node', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_node', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_node', 'symlink_install': False, 'test_result_base': None}
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_args' from command line to 'None'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target' from command line to 'None'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_cache' from command line to 'False'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_first' from command line to 'False'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_force_configure' from command line to 'False'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'ament_cmake_args' from command line to 'None'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_cmake_args' from command line to 'None'
[0.335s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.335s] DEBUG:colcon.colcon_core.verb:Building package 'pkg1' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/pkg1', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/pkg1', 'merge_install': False, 'path': '/home/<USER>/code/test/src/pkg1', 'symlink_install': False, 'test_result_base': None}
[0.335s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.336s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.336s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_node' with build type 'ament_cmake'
[0.336s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_node'
[0.339s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.339s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.339s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.342s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/pkg1' with build type 'ament_cmake'
[0.342s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/pkg1'
[0.342s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.342s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.349s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_node': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_node -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_node
[0.355s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.458s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.473s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.483s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.484s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.488s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.488s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.488s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.489s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.489s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.490s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.491s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.492s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.493s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.494s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[0.495s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.495s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.496s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.497s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.497s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.498s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.498s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.499s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.499s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[1.467s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_node' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_node -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_node
[1.469s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_node': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_node -- -j16 -l16
[3.756s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_node' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_node -- -j16 -l16
[3.757s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_node': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_node
[3.768s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_node)
[3.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node' for CMake module files
[3.769s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_node' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_node
[3.769s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node' for CMake config files
[3.770s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_node', 'cmake_prefix_path')
[3.770s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_node/share/learn_node/hook/cmake_prefix_path.ps1'
[3.770s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_node/share/learn_node/hook/cmake_prefix_path.dsv'
[3.770s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_node/share/learn_node/hook/cmake_prefix_path.sh'
[3.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/lib'
[3.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/bin'
[3.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/lib/pkgconfig/learn_node.pc'
[3.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/lib/python3.10/site-packages'
[3.771s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/bin'
[3.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.ps1'
[3.772s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_node/share/learn_node/package.dsv'
[3.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.sh'
[3.773s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.bash'
[3.773s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.zsh'
[3.773s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_node/share/colcon-core/packages/learn_node)
[3.773s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_node)
[3.774s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node' for CMake module files
[3.774s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node' for CMake config files
[3.774s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_node', 'cmake_prefix_path')
[3.774s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_node/share/learn_node/hook/cmake_prefix_path.ps1'
[3.775s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_node/share/learn_node/hook/cmake_prefix_path.dsv'
[3.775s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_node/share/learn_node/hook/cmake_prefix_path.sh'
[3.775s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/lib'
[3.775s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/bin'
[3.775s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/lib/pkgconfig/learn_node.pc'
[3.775s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/lib/python3.10/site-packages'
[3.775s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_node/bin'
[3.776s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.ps1'
[3.776s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_node/share/learn_node/package.dsv'
[3.776s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.sh'
[3.776s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.bash'
[3.777s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_node/share/learn_node/package.zsh'
[3.777s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_node/share/colcon-core/packages/learn_node)
[3.777s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[3.777s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[3.777s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[3.778s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.782s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[3.782s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.782s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[3.797s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.798s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.ps1'
[3.799s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_ps1.py'
[3.800s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.ps1'
[3.801s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.sh'
[3.801s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_sh.py'
[3.801s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.sh'
[3.802s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.bash'
[3.802s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.bash'
[3.803s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.zsh'
[3.804s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.zsh'
