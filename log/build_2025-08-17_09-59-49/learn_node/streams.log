[0.012s] Invoking command in '/home/<USER>/code/test/build/learn_node': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_node -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_node
[0.096s] -- The C compiler identification is GNU 11.4.0
[0.167s] -- The CXX compiler identification is GNU 11.4.0
[0.176s] -- Detecting C compiler ABI info
[0.255s] -- Detecting C compiler ABI info - done
[0.260s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.260s] -- Detecting C compile features
[0.261s] -- Detecting C compile features - done
[0.264s] -- Detecting CXX compiler ABI info
[0.345s] -- Detecting CXX compiler ABI info - done
[0.350s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.351s] -- Detecting CXX compile features
[0.351s] -- Detecting CXX compile features - done
[0.354s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.493s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.572s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.606s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.610s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.618s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.629s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.641s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.681s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.683s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.778s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.800s] -- Found FastRTPS: /opt/ros/humble/include  
[0.840s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.848s] -- Looking for pthread.h
[0.923s] -- Looking for pthread.h - found
[0.924s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.996s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.996s] -- Found Threads: TRUE  
[1.041s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.102s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.102s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/code/test/src/learn_node/include>
[1.102s] -- Configured cppcheck exclude dirs and/or files: 
[1.102s] -- Added test 'lint_cmake' to check CMake code style
[1.103s] -- Added test 'uncrustify' to check C / C++ code style
[1.103s] -- Configured uncrustify additional arguments: 
[1.104s] -- Added test 'xmllint' to check XML markup files
[1.105s] -- Configuring done
[1.114s] -- Generating done
[1.118s] -- Build files have been written to: /home/<USER>/code/test/build/learn_node
[1.130s] Invoked command in '/home/<USER>/code/test/build/learn_node' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_node -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_node
[1.132s] Invoking command in '/home/<USER>/code/test/build/learn_node': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_node -- -j16 -l16
[1.184s] [ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[3.317s] [100%] [32m[1mLinking CXX executable helloworld[0m
[3.407s] [100%] Built target helloworld
[3.419s] Invoked command in '/home/<USER>/code/test/build/learn_node' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_node -- -j16 -l16
[3.420s] Invoking command in '/home/<USER>/code/test/build/learn_node': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_node
[3.427s] -- Install configuration: ""
[3.428s] -- Installing: /home/<USER>/code/test/install/learn_node/lib/learn_node/helloworld
[3.428s] -- Set runtime path of "/home/<USER>/code/test/install/learn_node/lib/learn_node/helloworld" to ""
[3.428s] -- Installing: /home/<USER>/code/test/install/learn_node/share/ament_index/resource_index/package_run_dependencies/learn_node
[3.428s] -- Installing: /home/<USER>/code/test/install/learn_node/share/ament_index/resource_index/parent_prefix_path/learn_node
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/environment/ament_prefix_path.sh
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/environment/ament_prefix_path.dsv
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/environment/path.sh
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/environment/path.dsv
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/local_setup.bash
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/local_setup.sh
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/local_setup.zsh
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/local_setup.dsv
[3.429s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/package.dsv
[3.430s] -- Installing: /home/<USER>/code/test/install/learn_node/share/ament_index/resource_index/packages/learn_node
[3.430s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/cmake/learn_nodeConfig.cmake
[3.430s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/cmake/learn_nodeConfig-version.cmake
[3.430s] -- Installing: /home/<USER>/code/test/install/learn_node/share/learn_node/package.xml
[3.432s] Invoked command in '/home/<USER>/code/test/build/learn_node' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_node
