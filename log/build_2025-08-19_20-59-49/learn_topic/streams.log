[0.010s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.031s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.127s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.148s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.149s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.154s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.162s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.170s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.194s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.194s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.279s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.313s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.333s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.344s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.396s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.397s] -- Configured cppcheck include dirs: 
[0.397s] -- Configured cppcheck exclude dirs and/or files: 
[0.397s] -- Added test 'lint_cmake' to check CMake code style
[0.398s] -- Added test 'uncrustify' to check C / C++ code style
[0.398s] -- Configured uncrustify additional arguments: 
[0.398s] -- Added test 'xmllint' to check XML markup files
[0.399s] -- Configuring done
[0.418s] -- Generating done
[0.424s] -- Build files have been written to: /home/<USER>/code/test/build/learn_topic
[0.451s] [35m[1mConsolidate compiler generated dependencies of target pub_hello_world[0m
[0.451s] [35m[1mConsolidate compiler generated dependencies of target sub_hello_world[0m
[0.451s] [35m[1mConsolidate compiler generated dependencies of target pub_img[0m
[0.460s] [ 12%] [32mBuilding CXX object CMakeFiles/sub_img.dir/src/sub_img.cpp.o[0m
[0.469s] [ 37%] Built target pub_hello_world
[0.469s] [ 62%] Built target sub_hello_world
[0.474s] [ 87%] Built target pub_img
[7.622s] [100%] [32m[1mLinking CXX executable sub_img[0m
[8.206s] [100%] Built target sub_img
[8.218s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[8.228s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[8.236s] -- Install configuration: ""
[8.236s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[8.236s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world
[8.236s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_img
[8.236s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_img
[8.241s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_img" to ""
[8.241s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[8.241s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[8.241s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[8.242s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[8.242s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[8.242s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[8.242s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[8.242s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[8.244s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
