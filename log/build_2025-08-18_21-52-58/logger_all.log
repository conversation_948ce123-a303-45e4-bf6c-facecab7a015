[0.071s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.071s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7fedc1a332e0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fedc1a32e90>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fedc1a32e90>>)
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.182s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.182s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/code/test'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.183s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(src/learn_topic) by extension 'ros'
[0.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/learn_topic' with type 'ros.ament_cmake' and name 'learn_topic'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg1) by extension 'ros'
[0.195s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg1' with type 'ros.ament_cmake' and name 'pkg1'
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.195s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.209s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.210s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 2 installed packages in /home/<USER>/code/test/install
[0.211s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 272 installed packages in /opt/ros/humble
[0.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_cache' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_clean_first' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'cmake_force_configure' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'ament_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'learn_topic' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.248s] DEBUG:colcon.colcon_core.verb:Building package 'learn_topic' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/learn_topic', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/learn_topic', 'merge_install': False, 'path': '/home/<USER>/code/test/src/learn_topic', 'symlink_install': False, 'test_result_base': None}
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_cache' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_clean_first' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'cmake_force_configure' from command line to 'False'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'ament_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_cmake_args' from command line to 'None'
[0.248s] Level 5:colcon.colcon_core.verb:set package 'pkg1' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.248s] DEBUG:colcon.colcon_core.verb:Building package 'pkg1' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/code/test/build/pkg1', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/code/test/install/pkg1', 'merge_install': False, 'path': '/home/<USER>/code/test/src/pkg1', 'symlink_install': False, 'test_result_base': None}
[0.248s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.249s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.249s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/learn_topic' with build type 'ament_cmake'
[0.249s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/learn_topic'
[0.251s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.251s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.251s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.254s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/code/test/src/pkg1' with build type 'ament_cmake'
[0.255s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/code/test/src/pkg1'
[0.255s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.255s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.261s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.263s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.322s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.333s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.343s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.343s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[0.345s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.345s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.345s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.345s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.346s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.346s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.347s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.347s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.348s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.348s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.349s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.349s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.350s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[0.350s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg1)
[0.350s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake module files
[0.351s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1' for CMake config files
[0.351s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg1', 'cmake_prefix_path')
[0.351s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.ps1'
[0.351s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.dsv'
[0.351s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/pkg1/share/pkg1/hook/cmake_prefix_path.sh'
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib'
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/pkgconfig/pkg1.pc'
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/lib/python3.10/site-packages'
[0.352s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/pkg1/bin'
[0.352s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.ps1'
[0.353s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv'
[0.353s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.sh'
[0.353s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.bash'
[0.354s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/pkg1/share/pkg1/package.zsh'
[0.354s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/pkg1/share/colcon-core/packages/pkg1)
[3.739s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[3.740s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[3.751s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_topic)
[3.752s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake module files
[3.752s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[3.753s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake config files
[3.753s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_topic', 'cmake_prefix_path')
[3.753s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.ps1'
[3.753s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.dsv'
[3.753s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.sh'
[3.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib'
[3.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[3.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/pkgconfig/learn_topic.pc'
[3.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/python3.10/site-packages'
[3.754s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[3.754s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.ps1'
[3.755s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv'
[3.755s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.sh'
[3.755s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.bash'
[3.755s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.zsh'
[3.756s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_topic/share/colcon-core/packages/learn_topic)
[3.756s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(learn_topic)
[3.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake module files
[3.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic' for CMake config files
[3.757s] Level 1:colcon.colcon_core.shell:create_environment_hook('learn_topic', 'cmake_prefix_path')
[3.757s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.ps1'
[3.757s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.dsv'
[3.757s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/code/test/install/learn_topic/share/learn_topic/hook/cmake_prefix_path.sh'
[3.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib'
[3.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[3.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/pkgconfig/learn_topic.pc'
[3.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/lib/python3.10/site-packages'
[3.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/code/test/install/learn_topic/bin'
[3.758s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.ps1'
[3.759s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv'
[3.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.sh'
[3.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.bash'
[3.760s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/code/test/install/learn_topic/share/learn_topic/package.zsh'
[3.760s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/code/test/install/learn_topic/share/colcon-core/packages/learn_topic)
[3.760s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[3.761s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[3.761s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[3.761s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[3.764s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[3.764s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[3.764s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[3.774s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[3.774s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.ps1'
[3.776s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_ps1.py'
[3.777s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.ps1'
[3.777s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.sh'
[3.778s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/code/test/install/_local_setup_util_sh.py'
[3.778s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.sh'
[3.779s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.bash'
[3.780s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.bash'
[3.781s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/code/test/install/local_setup.zsh'
[3.781s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/code/test/install/setup.zsh'
