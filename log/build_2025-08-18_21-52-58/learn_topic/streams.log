[0.011s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.050s] [35m[1mConsolidate compiler generated dependencies of target pub_hello_world[0m
[0.068s] [ 50%] [32mBuilding CXX object CMakeFiles/pub_hello_world.dir/src/pub_hello_world.cpp.o[0m
[3.332s] [100%] [32m[1mLinking CXX executable pub_hello_world[0m
[3.478s] [100%] Built target pub_hello_world
[3.489s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[3.490s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[3.498s] -- Install configuration: ""
[3.498s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[3.499s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world" to ""
[3.499s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[3.500s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[3.500s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[3.501s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[3.501s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[3.502s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
