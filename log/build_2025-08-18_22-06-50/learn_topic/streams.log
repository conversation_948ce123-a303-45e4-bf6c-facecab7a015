[0.010s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[0.033s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.128s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.148s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.150s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.154s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.161s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.170s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.193s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.194s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.280s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.315s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.365s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.365s] -- Configured cppcheck include dirs: 
[0.365s] -- Configured cppcheck exclude dirs and/or files: 
[0.365s] -- Added test 'lint_cmake' to check CMake code style
[0.366s] -- Added test 'uncrustify' to check C / C++ code style
[0.366s] -- Configured uncrustify additional arguments: 
[0.366s] -- Added test 'xmllint' to check XML markup files
[0.367s] -- Configuring done
[0.376s] -- Generating done
[0.380s] -- Build files have been written to: /home/<USER>/code/test/build/learn_topic
[0.403s] [35m[1mConsolidate compiler generated dependencies of target pub_hello_world[0m
[0.413s] [ 25%] [32mBuilding CXX object CMakeFiles/sub_hello_world.dir/src/sub_hello_world.cpp.o[0m
[0.422s] [ 75%] Built target pub_hello_world
[6.031s] [100%] [32m[1mLinking CXX executable sub_hello_world[0m
[6.284s] [100%] Built target sub_hello_world
[6.295s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_topic -- -j16 -l16
[6.296s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
[6.302s] -- Install configuration: ""
[6.302s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/pub_hello_world
[6.303s] -- Installing: /home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world
[6.306s] -- Set runtime path of "/home/<USER>/code/test/install/learn_topic/lib/learn_topic/sub_hello_world" to ""
[6.307s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/package_run_dependencies/learn_topic
[6.307s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/parent_prefix_path/learn_topic
[6.307s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.sh
[6.307s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/ament_prefix_path.dsv
[6.307s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.sh
[6.307s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/environment/path.dsv
[6.307s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.bash
[6.307s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.sh
[6.308s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.zsh
[6.308s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/local_setup.dsv
[6.308s] -- Installing: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.dsv
[6.308s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/ament_index/resource_index/packages/learn_topic
[6.308s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig.cmake
[6.308s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/cmake/learn_topicConfig-version.cmake
[6.308s] -- Up-to-date: /home/<USER>/code/test/install/learn_topic/share/learn_topic/package.xml
[6.310s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_topic
