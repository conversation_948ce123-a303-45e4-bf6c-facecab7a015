[0.009s] Invoking command in '/home/<USER>/code/test/build/learn_topic': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
[0.056s] -- The C compiler identification is GNU 11.4.0
[0.101s] -- The CXX compiler identification is GNU 11.4.0
[0.108s] -- Detecting C compiler ABI info
[0.168s] -- Detecting C compiler ABI info - done
[0.173s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.173s] -- Detecting C compile features
[0.173s] -- Detecting C compile features - done
[0.175s] -- Detecting CXX compiler ABI info
[0.244s] -- Detecting CXX compiler ABI info - done
[0.249s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.250s] -- Detecting CXX compile features
[0.250s] -- Detecting CXX compile features - done
[0.252s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.364s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.439s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.469s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.473s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.479s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.487s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.500s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.534s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.536s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.617s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.640s] -- Found FastRTPS: /opt/ros/humble/include  
[0.675s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.681s] -- Looking for pthread.h
[0.746s] -- Looking for pthread.h - found
[0.747s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.812s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.813s] -- Found Threads: TRUE  
[0.850s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.871s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.881s] -- Found OpenCV: /usr (found version "4.5.4") 
[0.884s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.943s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.943s] -- Configured cppcheck include dirs: 
[0.943s] -- Configured cppcheck exclude dirs and/or files: 
[0.943s] -- Added test 'lint_cmake' to check CMake code style
[0.944s] -- Added test 'uncrustify' to check C / C++ code style
[0.944s] -- Configured uncrustify additional arguments: 
[0.944s] -- Added test 'xmllint' to check XML markup files
[0.945s] -- Configuring done
[0.950s] [31mCMake Error at CMakeLists.txt:35 (add_executable):
[0.950s]   Cannot find source file:
[0.950s] 
[0.950s]     src/pub_img_sim.cpp
[0.950s] 
[0.950s]   Tried extensions .c .C .c++ .cc .cpp .cxx .cu .mpp .m .M .mm .ixx .cppm .h
[0.950s]   .hh .h++ .hm .hpp .hxx .in .txx .f .F .for .f77 .f90 .f95 .f03 .hip .ispc
[0.950s] 
[0.950s] [0m
[0.951s] [31mCMake Error at CMakeLists.txt:35 (add_executable):
[0.951s]   No SOURCES given to target: pub_img_sim
[0.951s] 
[0.951s] [0m
[0.951s] [0mCMake Generate step failed.  Build files cannot be regenerated correctly.[0m
[0.960s] Invoked command in '/home/<USER>/code/test/build/learn_topic' returned '1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/learn_topic -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/learn_topic
