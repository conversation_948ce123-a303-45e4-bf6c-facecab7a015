[0.007s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[0.051s] -- The C compiler identification is GNU 11.4.0
[0.096s] -- The CXX compiler identification is GNU 11.4.0
[0.103s] -- Detecting C compiler ABI info
[0.164s] -- Detecting C compiler ABI info - done
[0.168s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.169s] -- Detecting C compile features
[0.169s] -- Detecting C compile features - done
[0.171s] -- Detecting CXX compiler ABI info
[0.239s] -- Detecting CXX compiler ABI info - done
[0.244s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.244s] -- Detecting CXX compile features
[0.244s] -- Detecting CXX compile features - done
[0.246s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.360s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.436s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.465s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.468s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.474s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.482s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.497s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.530s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.531s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.612s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.636s] -- Found FastRTPS: /opt/ros/humble/include  
[0.671s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.677s] -- Looking for pthread.h
[0.742s] -- Looking for pthread.h - found
[0.743s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.809s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.809s] -- Found Threads: TRUE  
[0.846s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.903s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.903s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/code/test/src/pkg1/include>
[0.903s] -- Configured cppcheck exclude dirs and/or files: 
[0.903s] -- Added test 'lint_cmake' to check CMake code style
[0.904s] -- Added test 'uncrustify' to check C / C++ code style
[0.904s] -- Configured uncrustify additional arguments: 
[0.905s] -- Added test 'xmllint' to check XML markup files
[0.906s] -- Configuring done
[0.913s] -- Generating done
[0.915s] -- Build files have been written to: /home/<USER>/code/test/build/pkg1
[0.925s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[0.927s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[0.976s] [ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[3.081s] [100%] [32m[1mLinking CXX executable helloworld[0m
[3.167s] [100%] Built target helloworld
