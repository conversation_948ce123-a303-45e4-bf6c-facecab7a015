In file included from [01m[K/usr/include/c++/11/pstl/glue_algorithm_defs.h:13[m[K,
                 from [01m[K/usr/include/c++/11/algorithm:74[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:1[m[K:
/usr/include/c++/11/functional: In instantiation of ‘[01m[Kstruct std::_Bind_check_arity<void (Service::*)(), Service*, const std::_Placeholder<1>&, const std::_Placeholder<2>&>[m[K’:
[01m[K/usr/include/c++/11/functional:768:12:[m[K   required from ‘[01m[Kstruct std::_Bind_helper<false, void (Service::*)(), Service*, const std::_Placeholder<1>&, const std::_Placeholder<2>&>[m[K’
[01m[K/usr/include/c++/11/functional:789:5:[m[K   required by substitution of ‘[01m[Ktemplate<class _Func, class ... _BoundArgs> typename std::_Bind_helper<std::__is_socketlike<_Func>::value, _Func, _BoundArgs ...>::type std::bind(_Func&&, _BoundArgs&& ...) [with _Func = void (Service::*)(); _BoundArgs = {Service*, const std::_Placeholder<1>&, const std::_Placeholder<2>&}][m[K’
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:27:22:[m[K   required from here
[01m[K/usr/include/c++/11/functional:756:21:[m[K [01;31m[Kerror: [m[Kstatic assertion failed: Wrong number of arguments for pointer-to-member
  755 |       static_assert(_Varargs::[01;31m[Kvalue[m[K
      |                               [01;31m[K~~~~~[m[K
  756 | [01;31m[K                    ? sizeof...(_BoundArgs) >= _Arity::value + 1[m[K
      |                     [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
  757 | [01;31m[K                    : sizeof...(_BoundArgs) == _Arity::value + 1[m[K,
      |                     [01;31m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/usr/include/c++/11/functional:756:21:[m[K [01;36m[Knote: [m[K‘[01m[K(false ? (3 >= (((long unsigned int)std::integral_constant<long unsigned int, 0>::value) + 1)) : (3 == (((long unsigned int)std::integral_constant<long unsigned int, 0>::value) + 1)))[m[K’ evaluates to false
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:35:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid Service::add_two_ints_callback(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)[m[K’
   35 | void [01;31m[KService[m[K::add_two_ints_callback(
      |      [01;31m[K^~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:13:10:[m[K [01;36m[Knote: [m[Kcandidate is: ‘[01m[Kvoid Service::add_two_ints_callback()[m[K’
   13 |     void [01;36m[Kadd_two_ints_callback[m[K();
      |          [01;36m[K^~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:6:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass Service[m[K’ defined here
    6 | class [01;36m[KService[m[K : public rclcpp::Node
      |       [01;36m[K^~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:27:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid ServiceClinet::send_request(int64_t, int64_t)[m[K’
   27 | void [01;31m[KServiceClinet[m[K::send_request(int64_t a, int64_t b)
      |      [01;31m[K^~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:13:10:[m[K [01;36m[Knote: [m[Kcandidate is: ‘[01m[Kvoid ServiceClinet::send_request()[m[K’
   13 |     void [01;36m[Ksend_request[m[K();
      |          [01;36m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:6:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass ServiceClinet[m[K’ defined here
    6 | class [01;36m[KServiceClinet[m[K : public rclcpp::Node
      |       [01;36m[K^~~~~~~~~~~~~[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/logging.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:40[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:1[m[K:
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:76:56:[m[K [01;31m[Kerror: [m[Kinvalid use of non-static member function ‘[01m[Kvirtual const char* std::exception::what() const[m[K’
   76 |       RCLCPP_INFO(rclcpp::get_logger("exception:%s"),[01;31m[Ke.what[m[K);
      |                                                      [01;31m[K~~^~~~[m[K
In file included from [01m[K/usr/include/c++/11/new:41[m[K,
                 from [01m[K/usr/include/c++/11/ext/new_allocator.h:33[m[K,
                 from [01m[K/usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h:33[m[K,
                 from [01m[K/usr/include/c++/11/bits/allocator.h:46[m[K,
                 from [01m[K/usr/include/c++/11/memory:64[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:153[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:1[m[K:
[01m[K/usr/include/c++/11/bits/exception.h:76:5:[m[K [01;36m[Knote: [m[Kdeclared here
   76 |     [01;36m[Kwhat[m[K() const _GLIBCXX_TXN_SAFE_DYN _GLIBCXX_NOTHROW;
      |     [01;36m[K^~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:81:41:[m[K [01;31m[Kerror: [m[K‘[01m[KService[m[K’ was not declared in this scope; did you mean ‘[01m[Krclcpp::Service[m[K’?
   81 |     auto client_node = std::make_shared<[01;31m[KService[m[K>();
      |                                         [01;31m[K^~~~~~~[m[K
      |                                         [32m[Krclcpp::Service[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/service.hpp:36[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:28[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:1[m[K:
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp:55:7:[m[K [01;36m[Knote: [m[K‘[01m[Krclcpp::Service[m[K’ declared here
   55 | class [01;36m[KService[m[K;
      |       [01;36m[K^~~~~~~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:81:49:[m[K [01;31m[Kerror: [m[Kno matching function for call to ‘[01m[Kmake_shared<<expression error> >()[m[K’
   81 |     auto client_node = [01;31m[Kstd::make_shared<Service>()[m[K;
      |                        [01;31m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~[m[K
In file included from [01m[K/usr/include/c++/11/memory:77[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:153[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:1[m[K:
[01m[K/usr/include/c++/11/bits/shared_ptr.h:875:5:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Ktemplate<class _Tp, class ... _Args> std::shared_ptr<_Tp> std::make_shared(_Args&& ...)[m[K’
  875 |     [01;36m[Kmake_shared[m[K(_Args&&... __args)
      |     [01;36m[K^~~~~~~~~~~[m[K
[01m[K/usr/include/c++/11/bits/shared_ptr.h:875:5:[m[K [01;36m[Knote: [m[K  template argument deduction/substitution failed:
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:81:49:[m[K [01;31m[Kerror: [m[Ktemplate argument 1 is invalid
   81 |     auto client_node = [01;31m[Kstd::make_shared<Service>()[m[K;
      |                        [01;31m[K~~~~~~~~~~~~~~~~~~~~~~~~~^~[m[K
[01m[K/home/<USER>/code/test/src/learn_service/src/service_client.cpp:83:18:[m[K [01;31m[Kerror: [m[K‘[01m[Knode[m[K’ was not declared in this scope
   83 |     rclcpp::spin([01;31m[Knode[m[K);
      |                  [01;31m[K^~~~[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/service.hpp:36[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:28[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:1[m[K:
/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp: In instantiation of ‘[01m[Kvoid rclcpp::AnyServiceCallback<ServiceT>::set(CallbackT&&) [with CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename std::enable_if<(! rclcpp::detail::can_be_nullptr<CallbackT>::value), int>::type <anonymous> = 0; ServiceT = example_interfaces::srv::AddTwoInts][m[K’:
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/create_service.hpp:43:27:[m[K   required from ‘[01m[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::create_service(std::shared_ptr<rclcpp::node_interfaces::NodeBaseInterface>, std::shared_ptr<rclcpp::node_interfaces::NodeServicesInterface>, const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>][m[K’
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/node_impl.hpp:147:53:[m[K   required from ‘[01m[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::Node::create_service(const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>][m[K’
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:25:73:[m[K   required from here
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp:103:17:[m[K [01;31m[Kerror: [m[Kno match for ‘[01m[Koperator=[m[K’ (operand types are ‘[01m[Kstd::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >[m[K’ and ‘[01m[Kstd::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>[m[K’)
  103 |       [01;31m[Kcallback_ = std::forward<CallbackT>(callback)[m[K;
      |       [01;31m[K~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:28[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:1[m[K:
[01m[K/usr/include/c++/11/variant:1461:9:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Ktemplate<class _Tp> std::enable_if_t<((__exactly_once<std::variant<_Types>::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type> > && is_constructible_v<std::variant<_Types>::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>, _Tp>) && is_assignable_v<std::variant<_Types>::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>&, _Tp>), std::variant<_Types>&> std::variant<_Types>::operator=(_Tp&&) [with _Tp = _Tp; _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}][m[K’
 1461 |         [01;36m[Koperator[m[K=(_Tp&& __rhs)
      |         [01;36m[K^~~~~~~~[m[K
[01m[K/usr/include/c++/11/variant:1461:9:[m[K [01;36m[Knote: [m[K  template argument deduction/substitution failed:
/usr/include/c++/11/variant: In substitution of ‘[01m[Ktemplate<class ... _Types> template<class _Tp, class> using __accepted_type = std::variant<_Types>::__to_type<__accepted_index<_Tp> > [with _Tp = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>&&; <template-parameter-2-2> = void; _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}][m[K’:
[01m[K/usr/include/c++/11/variant:1457:14:[m[K   required by substitution of ‘[01m[Ktemplate<class _Tp> std::enable_if_t<((__exactly_once<std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type> > && is_constructible_v<std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>, _Tp>) && is_assignable_v<std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::__accepted_type<_Tp&&, typename std::enable_if<__not_self<_Tp&&>, void>::type>&, _Tp>), std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >&> std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >::operator=<_Tp>(_Tp&&) [with _Tp = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>][m[K’
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp:103:17:[m[K   required from ‘[01m[Kvoid rclcpp::AnyServiceCallback<ServiceT>::set(CallbackT&&) [with CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename std::enable_if<(! rclcpp::detail::can_be_nullptr<CallbackT>::value), int>::type <anonymous> = 0; ServiceT = example_interfaces::srv::AddTwoInts][m[K’
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/create_service.hpp:43:27:[m[K   required from ‘[01m[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::create_service(std::shared_ptr<rclcpp::node_interfaces::NodeBaseInterface>, std::shared_ptr<rclcpp::node_interfaces::NodeServicesInterface>, const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>][m[K’
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/node_impl.hpp:147:53:[m[K   required from ‘[01m[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::Node::create_service(const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>][m[K’
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:25:73:[m[K   required from here
[01m[K/usr/include/c++/11/variant:1375:15:[m[K [01;31m[Kerror: [m[Kno type named ‘[01m[Ktype[m[K’ in ‘[01m[Kstruct std::enable_if<false, void>[m[K’
 1375 |         using [01;31m[K__accepted_type[m[K = __to_type<__accepted_index<_Tp>>;
      |               [01;31m[K^~~~~~~~~~~~~~~[m[K
/opt/ros/humble/include/rclcpp/rclcpp/any_service_callback.hpp: In instantiation of ‘[01m[Kvoid rclcpp::AnyServiceCallback<ServiceT>::set(CallbackT&&) [with CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename std::enable_if<(! rclcpp::detail::can_be_nullptr<CallbackT>::value), int>::type <anonymous> = 0; ServiceT = example_interfaces::srv::AddTwoInts][m[K’:
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/create_service.hpp:43:27:[m[K   required from ‘[01m[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::create_service(std::shared_ptr<rclcpp::node_interfaces::NodeBaseInterface>, std::shared_ptr<rclcpp::node_interfaces::NodeServicesInterface>, const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>][m[K’
[01m[K/opt/ros/humble/include/rclcpp/rclcpp/node_impl.hpp:147:53:[m[K   required from ‘[01m[Ktypename rclcpp::Service<ServiceT>::SharedPtr rclcpp::Node::create_service(const string&, CallbackT&&, const rmw_qos_profile_t&, rclcpp::CallbackGroup::SharedPtr) [with ServiceT = example_interfaces::srv::AddTwoInts; CallbackT = std::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>; typename rclcpp::Service<ServiceT>::SharedPtr = std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >; std::string = std::__cxx11::basic_string<char>; rmw_qos_profile_t = rmw_qos_profile_s; rclcpp::CallbackGroup::SharedPtr = std::shared_ptr<rclcpp::CallbackGroup>][m[K’
[01m[K/home/<USER>/code/test/src/learn_service/src/service_server.cpp:25:73:[m[K   required from here
[01m[K/usr/include/c++/11/variant:1398:16:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Kstd::variant<_Types>& std::variant<_Types>::operator=(const std::variant<_Types>&) [with _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}][m[K’
 1398 |       variant& [01;36m[Koperator[m[K=(const variant&) = default;
      |                [01;36m[K^~~~~~~~[m[K
[01m[K/usr/include/c++/11/variant:1398:26:[m[K [01;36m[Knote: [m[K  no known conversion for argument 1 from ‘[01m[Kstd::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>[m[K’ to ‘[01m[Kconst std::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >&[m[K’
 1398 |       variant& operator=([01;36m[Kconst variant&[m[K) = default;
      |                          [01;36m[K^~~~~~~~~~~~~~[m[K
[01m[K/usr/include/c++/11/variant:1399:16:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Kstd::variant<_Types>& std::variant<_Types>::operator=(std::variant<_Types>&&) [with _Types = {std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>}][m[K’
 1399 |       variant& [01;36m[Koperator[m[K=(variant&&) = default;
      |                [01;36m[K^~~~~~~~[m[K
[01m[K/usr/include/c++/11/variant:1399:26:[m[K [01;36m[Knote: [m[K  no known conversion for argument 1 from ‘[01m[Kstd::_Bind<void (Service::*(Service*, std::_Placeholder<1>, std::_Placeholder<2>))()>[m[K’ to ‘[01m[Kstd::variant<std::monostate, std::function<void(std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >, std::shared_ptr<example_interfaces::srv::AddTwoInts_Response_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)>, std::function<void(std::shared_ptr<rclcpp::Service<example_interfaces::srv::AddTwoInts> >, std::shared_ptr<rmw_request_id_s>, std::shared_ptr<example_interfaces::srv::AddTwoInts_Request_<std::allocator<void> > >)> >&&[m[K’
 1399 |       variant& operator=([01;36m[Kvariant&&[m[K) = default;
      |                          [01;36m[K^~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/service_server.dir/build.make:76：CMakeFiles/service_server.dir/src/service_server.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:165：CMakeFiles/service_server.dir/all] 错误 2
gmake[1]: *** 正在等待未完成的任务....
gmake[2]: *** [CMakeFiles/service_client.dir/build.make:76：CMakeFiles/service_client.dir/src/service_client.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/service_client.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
