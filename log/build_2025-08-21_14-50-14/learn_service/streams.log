[0.009s] Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[0.050s] [35m[1mConsolidate compiler generated dependencies of target service_client[0m
[0.066s] [ 50%] Built target service_server
[0.070s] [ 75%] [32mBuilding CXX object CMakeFiles/service_client.dir/src/service_client.cpp.o[0m
[2.787s] [100%] [32m[1mLinking CXX executable service_client[0m
[2.909s] [100%] Built target service_client
[2.920s] Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/learn_service -- -j16 -l16
[2.930s] Invoking command in '/home/<USER>/code/test/build/learn_service': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
[2.938s] -- Install configuration: ""
[2.938s] -- Installing: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_server
[2.939s] -- Set runtime path of "/home/<USER>/code/test/install/learn_service/lib/learn_service/service_server" to ""
[2.939s] -- Installing: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_client
[2.939s] -- Set runtime path of "/home/<USER>/code/test/install/learn_service/lib/learn_service/service_client" to ""
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/package_run_dependencies/learn_service
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/parent_prefix_path/learn_service
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.sh
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.dsv
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.sh
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.dsv
[2.940s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.bash
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.sh
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.zsh
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.dsv
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/packages/learn_service
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig.cmake
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig-version.cmake
[2.941s] -- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/package.xml
[2.943s] Invoked command in '/home/<USER>/code/test/build/learn_service' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/learn_service
