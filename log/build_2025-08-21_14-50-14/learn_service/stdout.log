[35m[1mConsolidate compiler generated dependencies of target service_client[0m
[ 50%] Built target service_server
[ 75%] [32mBuilding CXX object CMakeFiles/service_client.dir/src/service_client.cpp.o[0m
[100%] [32m[1mLinking CXX executable service_client[0m
[100%] Built target service_client
-- Install configuration: ""
-- Installing: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_server
-- Set runtime path of "/home/<USER>/code/test/install/learn_service/lib/learn_service/service_server" to ""
-- Installing: /home/<USER>/code/test/install/learn_service/lib/learn_service/service_client
-- Set runtime path of "/home/<USER>/code/test/install/learn_service/lib/learn_service/service_client" to ""
-- Installing: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/package_run_dependencies/learn_service
-- Installing: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/parent_prefix_path/learn_service
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.sh
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/environment/path.dsv
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.bash
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.sh
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.zsh
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/local_setup.dsv
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/package.dsv
-- Installing: /home/<USER>/code/test/install/learn_service/share/ament_index/resource_index/packages/learn_service
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig.cmake
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/cmake/learn_serviceConfig-version.cmake
-- Installing: /home/<USER>/code/test/install/learn_service/share/learn_service/package.xml
