[0.009s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[0.058s] -- The C compiler identification is GNU 11.4.0
[0.104s] -- The CXX compiler identification is GNU 11.4.0
[0.110s] -- Detecting C compiler ABI info
[0.173s] -- Detecting C compiler ABI info - done
[0.178s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.178s] -- Detecting C compile features
[0.178s] -- Detecting C compile features - done
[0.180s] -- Detecting CXX compiler ABI info
[0.253s] -- Detecting CXX compiler ABI info - done
[0.258s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.258s] -- Detecting CXX compile features
[0.259s] -- Detecting CXX compile features - done
[0.264s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.380s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.452s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.487s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.491s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.498s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.509s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.524s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.561s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.563s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.652s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.675s] -- Found FastRTPS: /opt/ros/humble/include  
[0.710s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.719s] -- Looking for pthread.h
[0.785s] -- Looking for pthread.h - found
[0.786s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.851s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.852s] -- Found Threads: TRUE  
[0.895s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.953s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.953s] -- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/code/test/src/pkg1/include>
[0.953s] -- Configured cppcheck exclude dirs and/or files: 
[0.953s] -- Added test 'lint_cmake' to check CMake code style
[0.954s] -- Added test 'uncrustify' to check C / C++ code style
[0.954s] -- Configured uncrustify additional arguments: 
[0.954s] -- Added test 'xmllint' to check XML markup files
[0.956s] -- Configuring done
[0.963s] -- Generating done
[0.965s] -- Build files have been written to: /home/<USER>/code/test/build/pkg1
[0.974s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/code/test/src/pkg1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/code/test/install/pkg1
[0.976s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[1.023s] [ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[1.058s] [100%] [32m[1mLinking CXX executable helloworld[0m
[1.116s] [100%] Built target helloworld
[1.127s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/code/test/build/pkg1 -- -j16 -l16
[1.137s] Invoking command in '/home/<USER>/code/test/build/pkg1': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
[1.143s] -- Install configuration: ""
[1.143s] -- Installing: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
[1.144s] -- Set runtime path of "/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld" to ""
[1.144s] -- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
[1.144s] -- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
[1.144s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
[1.144s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
[1.144s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
[1.144s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
[1.145s] -- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml
[1.147s] Invoked command in '/home/<USER>/code/test/build/pkg1' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/code/test/build/pkg1
