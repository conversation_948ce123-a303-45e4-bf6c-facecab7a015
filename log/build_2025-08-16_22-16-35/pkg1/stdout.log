-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: $<BUILD_INTERFACE:/home/<USER>/code/test/src/pkg1/include>
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/code/test/build/pkg1
[ 50%] [32mBuilding CXX object CMakeFiles/helloworld.dir/src/helloworld.cpp.o[0m
[100%] [32m[1mLinking CXX executable helloworld[0m
[100%] Built target helloworld
-- Install configuration: ""
-- Installing: /home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
-- Set runtime path of "/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld" to ""
-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
-- Installing: /home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
-- Installing: /home/<USER>/code/test/install/pkg1/share/pkg1/package.xml
