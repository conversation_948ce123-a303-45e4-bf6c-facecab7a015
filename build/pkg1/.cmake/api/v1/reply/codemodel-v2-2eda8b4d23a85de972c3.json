{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-0b02598de2c782e59dc4.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "pkg1", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "helloworld::@6890427a1f51a3e7e1df", "jsonFile": "target-helloworld-a064192365bcc05debce.json", "name": "helloworld", "projectIndex": 0}, {"directoryIndex": 0, "id": "pkg1_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-pkg1_uninstall-ad8a59247fbfb3dbdcd2.json", "name": "pkg1_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-41f5845306a50e3eced7.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/test/build/pkg1", "source": "/home/<USER>/code/test/src/pkg1"}, "version": {"major": 2, "minor": 3}}