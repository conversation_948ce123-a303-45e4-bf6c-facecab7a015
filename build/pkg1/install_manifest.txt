/home/<USER>/code/test/install/pkg1/lib/pkg1/helloworld
/home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/package_run_dependencies/pkg1
/home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/parent_prefix_path/pkg1
/home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.sh
/home/<USER>/code/test/install/pkg1/share/pkg1/environment/ament_prefix_path.dsv
/home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.sh
/home/<USER>/code/test/install/pkg1/share/pkg1/environment/path.dsv
/home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.bash
/home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.sh
/home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.zsh
/home/<USER>/code/test/install/pkg1/share/pkg1/local_setup.dsv
/home/<USER>/code/test/install/pkg1/share/pkg1/package.dsv
/home/<USER>/code/test/install/pkg1/share/ament_index/resource_index/packages/pkg1
/home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config.cmake
/home/<USER>/code/test/install/pkg1/share/pkg1/cmake/pkg1Config-version.cmake
/home/<USER>/code/test/install/pkg1/share/pkg1/package.xml