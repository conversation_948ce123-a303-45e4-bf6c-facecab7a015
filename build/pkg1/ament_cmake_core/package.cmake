set(_AMENT_PACKAGE_NAME "pkg1")
set(pkg1_VERSION "0.0.0")
set(pkg1_MAINTAINER "wuliu <<EMAIL>>")
set(pkg1_BUILD_DEPENDS "rclcpp")
set(pkg1_BUILDTOOL_DEPENDS "ament_cmake")
set(pkg1_BUILD_EXPORT_DEPENDS "rclcpp")
set(pkg1_BUILDTOOL_EXPORT_DEPENDS )
set(pkg1_EXEC_DEPENDS "rclcpp")
set(pkg1_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(pkg1_GROUP_DEPENDS )
set(pkg1_MEMBER_OF_GROUPS )
set(pkg1_DEPRECATED "")
set(pkg1_EXPORT_TAGS)
list(APPEND pkg1_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
