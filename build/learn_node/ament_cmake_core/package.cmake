set(_AMENT_PACKAGE_NAME "learn_node")
set(learn_node_VERSION "0.0.0")
set(learn_node_MAINTAINER "wuliu <<EMAIL>>")
set(learn_node_BUILD_DEPENDS "rclcpp")
set(learn_node_BUILDTOOL_DEPENDS "ament_cmake")
set(learn_node_BUILD_EXPORT_DEPENDS "rclcpp")
set(learn_node_BUILDTOOL_EXPORT_DEPENDS )
set(learn_node_EXEC_DEPENDS "rclcpp")
set(learn_node_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(learn_node_GROUP_DEPENDS )
set(learn_node_MEMBER_OF_GROUPS )
set(learn_node_DEPRECATED "")
set(learn_node_EXPORT_TAGS)
list(APPEND learn_node_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
