{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-10cea61d7867bc0c3981.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "learn_node", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "helloworld::@6890427a1f51a3e7e1df", "jsonFile": "target-helloworld-18be9e2216e7fca7bb86.json", "name": "helloworld", "projectIndex": 0}, {"directoryIndex": 0, "id": "learn_node_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-learn_node_uninstall-b045ffe6cc0a1575e323.json", "name": "learn_node_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-28718a5d95a3667afa91.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/test/build/learn_node", "source": "/home/<USER>/code/test/src/learn_node"}, "version": {"major": 2, "minor": 3}}