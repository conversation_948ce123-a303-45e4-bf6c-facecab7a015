set(_AMENT_PACKAGE_NAME "learn_service")
set(learn_service_VERSION "0.0.0")
set(learn_service_MAINTAINER "wuliu <<EMAIL>>")
set(learn_service_BUILD_DEPENDS "rclcpp" "chrono" "example_interfaces")
set(learn_service_BUILDTOOL_DEPENDS "ament_cmake")
set(learn_service_BUILD_EXPORT_DEPENDS "example_interfaces")
set(learn_service_BUILDTOOL_EXPORT_DEPENDS )
set(learn_service_EXEC_DEPENDS "example_interfaces")
set(learn_service_TEST_DEPENDS )
set(learn_service_GROUP_DEPENDS )
set(learn_service_MEMBER_OF_GROUPS )
set(learn_service_DEPRECATED "")
set(learn_service_EXPORT_TAGS)
list(APPEND learn_service_EXPORT_TAGS "<build_type>cmake</build_type>")
