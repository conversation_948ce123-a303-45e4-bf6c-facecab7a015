# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/test/src/learn_topic

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/test/build/learn_topic

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/pub_hello_world.dir/all
all: CMakeFiles/sub_hello_world.dir/all
all: CMakeFiles/pub_img.dir/all
all: CMakeFiles/sub_img.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/uninstall.dir/clean
clean: CMakeFiles/learn_topic_uninstall.dir/clean
clean: CMakeFiles/pub_hello_world.dir/clean
clean: CMakeFiles/sub_hello_world.dir/clean
clean: CMakeFiles/pub_img.dir/clean
clean: CMakeFiles/sub_img.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/uninstall.dir

# All Build rule for target.
CMakeFiles/uninstall.dir/all: CMakeFiles/learn_topic_uninstall.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/test/build/learn_topic/CMakeFiles --progress-num= "Built target uninstall"
.PHONY : CMakeFiles/uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
.PHONY : CMakeFiles/uninstall.dir/rule

# Convenience name for target.
uninstall: CMakeFiles/uninstall.dir/rule
.PHONY : uninstall

# clean rule for target.
CMakeFiles/uninstall.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/clean
.PHONY : CMakeFiles/uninstall.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/learn_topic_uninstall.dir

# All Build rule for target.
CMakeFiles/learn_topic_uninstall.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/learn_topic_uninstall.dir/build.make CMakeFiles/learn_topic_uninstall.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/learn_topic_uninstall.dir/build.make CMakeFiles/learn_topic_uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/test/build/learn_topic/CMakeFiles --progress-num= "Built target learn_topic_uninstall"
.PHONY : CMakeFiles/learn_topic_uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/learn_topic_uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/learn_topic_uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
.PHONY : CMakeFiles/learn_topic_uninstall.dir/rule

# Convenience name for target.
learn_topic_uninstall: CMakeFiles/learn_topic_uninstall.dir/rule
.PHONY : learn_topic_uninstall

# clean rule for target.
CMakeFiles/learn_topic_uninstall.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/learn_topic_uninstall.dir/build.make CMakeFiles/learn_topic_uninstall.dir/clean
.PHONY : CMakeFiles/learn_topic_uninstall.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/pub_hello_world.dir

# All Build rule for target.
CMakeFiles/pub_hello_world.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pub_hello_world.dir/build.make CMakeFiles/pub_hello_world.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pub_hello_world.dir/build.make CMakeFiles/pub_hello_world.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/test/build/learn_topic/CMakeFiles --progress-num=1,2 "Built target pub_hello_world"
.PHONY : CMakeFiles/pub_hello_world.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pub_hello_world.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pub_hello_world.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
.PHONY : CMakeFiles/pub_hello_world.dir/rule

# Convenience name for target.
pub_hello_world: CMakeFiles/pub_hello_world.dir/rule
.PHONY : pub_hello_world

# clean rule for target.
CMakeFiles/pub_hello_world.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pub_hello_world.dir/build.make CMakeFiles/pub_hello_world.dir/clean
.PHONY : CMakeFiles/pub_hello_world.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/sub_hello_world.dir

# All Build rule for target.
CMakeFiles/sub_hello_world.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sub_hello_world.dir/build.make CMakeFiles/sub_hello_world.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sub_hello_world.dir/build.make CMakeFiles/sub_hello_world.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/test/build/learn_topic/CMakeFiles --progress-num=5,6 "Built target sub_hello_world"
.PHONY : CMakeFiles/sub_hello_world.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/sub_hello_world.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/sub_hello_world.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
.PHONY : CMakeFiles/sub_hello_world.dir/rule

# Convenience name for target.
sub_hello_world: CMakeFiles/sub_hello_world.dir/rule
.PHONY : sub_hello_world

# clean rule for target.
CMakeFiles/sub_hello_world.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sub_hello_world.dir/build.make CMakeFiles/sub_hello_world.dir/clean
.PHONY : CMakeFiles/sub_hello_world.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/pub_img.dir

# All Build rule for target.
CMakeFiles/pub_img.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pub_img.dir/build.make CMakeFiles/pub_img.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pub_img.dir/build.make CMakeFiles/pub_img.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/test/build/learn_topic/CMakeFiles --progress-num=3,4 "Built target pub_img"
.PHONY : CMakeFiles/pub_img.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pub_img.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pub_img.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
.PHONY : CMakeFiles/pub_img.dir/rule

# Convenience name for target.
pub_img: CMakeFiles/pub_img.dir/rule
.PHONY : pub_img

# clean rule for target.
CMakeFiles/pub_img.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pub_img.dir/build.make CMakeFiles/pub_img.dir/clean
.PHONY : CMakeFiles/pub_img.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/sub_img.dir

# All Build rule for target.
CMakeFiles/sub_img.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sub_img.dir/build.make CMakeFiles/sub_img.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sub_img.dir/build.make CMakeFiles/sub_img.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/test/build/learn_topic/CMakeFiles --progress-num=7,8 "Built target sub_img"
.PHONY : CMakeFiles/sub_img.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/sub_img.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/sub_img.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/test/build/learn_topic/CMakeFiles 0
.PHONY : CMakeFiles/sub_img.dir/rule

# Convenience name for target.
sub_img: CMakeFiles/sub_img.dir/rule
.PHONY : sub_img

# clean rule for target.
CMakeFiles/sub_img.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sub_img.dir/build.make CMakeFiles/sub_img.dir/clean
.PHONY : CMakeFiles/sub_img.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

