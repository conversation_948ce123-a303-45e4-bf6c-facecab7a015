set(_AMENT_PACKAGE_NAME "learn_topic")
set(learn_topic_VERSION "0.0.0")
set(learn_topic_MAINTAINER "wuliu <<EMAIL>>")
set(learn_topic_BUILD_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "cv_bridge" "opencv2")
set(learn_topic_BUILDTOOL_DEPENDS "ament_cmake")
set(learn_topic_BUILD_EXPORT_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "cv_bridge" "opencv2")
set(learn_topic_BUILDTOOL_EXPORT_DEPENDS )
set(learn_topic_EXEC_DEPENDS "rclcpp" "std_msgs" "sensor_msgs" "cv_bridge" "opencv2")
set(learn_topic_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(learn_topic_GROUP_DEPENDS )
set(learn_topic_MEMBER_OF_GROUPS )
set(learn_topic_DEPRECATED "")
set(learn_topic_EXPORT_TAGS)
list(APPEND learn_topic_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
