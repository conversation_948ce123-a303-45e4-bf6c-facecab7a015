{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-2bdb2b54fa8732d65b57.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "learn_topic", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "learn_topic_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-learn_topic_uninstall-c550d8c94b3e88a54461.json", "name": "learn_topic_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "pub_hello_world::@6890427a1f51a3e7e1df", "jsonFile": "target-pub_hello_world-3108b7c27f2949ac4bb2.json", "name": "pub_hello_world", "projectIndex": 0}, {"directoryIndex": 0, "id": "pub_img::@6890427a1f51a3e7e1df", "jsonFile": "target-pub_img-108dcd1d33cae0b25b75.json", "name": "pub_img", "projectIndex": 0}, {"directoryIndex": 0, "id": "sub_hello_world::@6890427a1f51a3e7e1df", "jsonFile": "target-sub_hello_world-2430acb2f45953b0fcdd.json", "name": "sub_hello_world", "projectIndex": 0}, {"directoryIndex": 0, "id": "sub_img::@6890427a1f51a3e7e1df", "jsonFile": "target-sub_img-158623f003f1977364fd.json", "name": "sub_img", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-5bc05c58fad10b546470.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/test/build/learn_topic", "source": "/home/<USER>/code/test/src/learn_topic"}, "version": {"major": 2, "minor": 3}}