{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-7dd3b4c31ad8aa8c1347.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "learn_topic", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "learn_topic_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-learn_topic_uninstall-c550d8c94b3e88a54461.json", "name": "learn_topic_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "pub_hello_world::@6890427a1f51a3e7e1df", "jsonFile": "target-pub_hello_world-420292898dd3eece12ef.json", "name": "pub_hello_world", "projectIndex": 0}, {"directoryIndex": 0, "id": "sub_hello_world::@6890427a1f51a3e7e1df", "jsonFile": "target-sub_hello_world-ab4f5add303bd079234d.json", "name": "sub_hello_world", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-5bc05c58fad10b546470.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/code/test/build/learn_topic", "source": "/home/<USER>/code/test/src/learn_topic"}, "version": {"major": 2, "minor": 3}}